{
  "plugins": ["prettier", "react", "import", "@typescript-eslint", "tailwindcss"],
  "extends": [
    // Base configurations
    "eslint:recommended",

    // Next.js configurations (already includes React and react-hooks)
    "next",
    "next/core-web-vitals",

    // TypeScript configurations
    "plugin:@typescript-eslint/recommended",

    // React specific rules (but remove react-hooks as it's already in Next.js config)
    "plugin:react/recommended",

    // Import plugin configurations
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript",

    // TailwindCSS configurations
    "plugin:tailwindcss/recommended",

    // Prettier must be last
    "prettier",
    "plugin:prettier/recommended"
  ],

  "overrides": [
    {
      "files": ["*.ts", "*.tsx", "*.js", "*.jsx"],

      "rules": {
        "react/display-name": "off",
        "react-hooks/exhaustive-deps": "off",
        "react/react-in-jsx-scope": "off",
        "react-hooks/rules-of-hooks": "error",
        "react/prop-types": "off",

        "prefer-const": "warn",
        "no-var": "error",
        "no-console": ["warn", { "allow": ["error", "warn"] }],

        "@typescript-eslint/no-unused-vars": ["warn", { "varsIgnorePattern": "^_", "argsIgnorePattern": "^_" }],
        "@typescript-eslint/no-explicit-any": "warn",
        "@typescript-eslint/ban-ts-comment": "warn",
        "@typescript-eslint/no-non-null-asserted-optional-chain": "warn",

        // "max-lines": ["warn", 500],

        "import/order": ["warn", { "groups": [["builtin", "external", "internal"]] }],
        "import/no-unresolved": "warn",
        "prettier/prettier": "warn",

        "tailwindcss/classnames-order": "off"
      }
    }
  ],
  "settings": { "react": { "version": "detect" } }
}
