'use client';

import {
  checkTestLectureIsExit,
  CourseDetailInfo,
  getLectureInCourse,
  PublishCourseRequest,
  Section,
  SectionCreateRequest,
  useCreateCrouseApi,
} from '@/features/courses';
import { revalidatePath } from 'actions/revalidatePath';
import { message, notification, RadioChangeEvent } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { LectureType, routePaths, SectionType } from 'config';
import { MAX_CREATE_FREE_COURSE } from 'constants/config';
import { AppMode, Display } from 'constants/enum';
import { InteractionType } from 'constants/index';
import { useLearner } from 'hooks/apis';
import { useCourse } from 'hooks/apis/course';
import { useQuerySearch } from 'hooks/useQuerySearch';
import { useRouter } from 'next-nprogress-bar';
import { useSearchParams } from 'next/navigation';
import queryString from 'query-string';
import { useContext, useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { setIsEmptySegmentName, setSelectedInteraction } from 'reducer/courseReducer/courseSlice';
import { setSelectedSlideItem } from 'reducer/courseReducer/slideSlice';
import { setTempSubmit } from 'reducer/courseReducer/testSlice';
import { RootState } from 'store/store';
import {
  CourseActive,
  CreateChapterBody,
  CreateTargetLearningBody,
  QueryParams,
  UpdateLearningGoalBody,
  UpdateLectureBody,
  UpdateLectureDocument,
  UpdateLectureSegment,
  UpdateSlides,
} from 'type';
import { getIndexInvalidInteraction, getSectionInCourse, inValidStudyTarget, notifyWarning } from 'utils';
import UserInfoContext from 'utils/providers/UserInfoProvider';
import { userIsVip } from 'utils/userIsVip';

import { getIndexInvalidCaseStudy } from '@/features/courses/';
import { SlideItem } from '@/features/courses/types';
import { LectureDetail } from '@/features/courses/types/lecture.type';
import { omit } from 'lodash-es';

export function useSectionContent({
  courseInfo,
  section,
}: Readonly<{ courseInfo: CourseDetailInfo; section?: Section }>) {
  const [form] = useForm();

  const dispatch = useDispatch();

  const segmentsStore = useSelector((state: RootState) => state.course.segments);
  const selectedLecture = useSelector((state: RootState) => state.course.selectedLecture);
  const lectureDocuments = useSelector((state: RootState) => state.textEditor.lectureDocuments);
  const totalTimeVideo = useSelector((state: RootState) => state.course.totalTimeVideo);

  const slides = useSelector((state: RootState) => state.slides.slides);
  const currentTest = useSelector((state: RootState) => state.test.currentTest);
  const currentTarget = useSelector((state: RootState) => state.target.targetStore);
  const currentStudyResult = useSelector((state: RootState) => state.target.studyResult);

  const queryData: QueryParams = useQuerySearch();
  const router = useRouter();

  const {
    getCourseById,
    createChapter,
    updateLectureSegment,
    getChapterById,
    updateLectureDocument,
    updateSlideItems,
    createTarget,
    updateTest,
    updateLectureInteraction,
    // releaseCourse,
    // publishChapter,
    updateLearningGoals,
    draftCourse,
  } = useCourse();

  const { createSection, publicCourse, createSectionTest } = useCreateCrouseApi();

  const { userInfo } = useContext(UserInfoContext);
  const { startLearnCourse } = useLearner();

  const { id: courseId } = courseInfo || {};

  const [isShowModalDesignContent, setIsShowModalDesignContent] = useState<boolean>(false);

  const searchParams = useSearchParams();

  const [query, setQuery] = useState({ ...queryString.parse(searchParams.toString()) });
  const [display, setDisplay] = useState<Display>(
    courseInfo?.sections && courseInfo?.sections?.length > 0 ? Display.ChapterList : Display.Default,
  );
  const [isShowChapter, setIsShowChapter] = useState<boolean>(
    !!(courseInfo?.sections && courseInfo?.sections?.length > 0),
  );
  const [sequentially, setSequentially] = useState<number>(0);
  const [isLoadingPreview, setIsLoadingPreview] = useState<boolean>(false);

  const lecturerInteracts = selectedLecture.lectureInteracts || [];

  const refetchChapter = () => router.refresh();
  const refetch = () => router.refresh();

  const { isLoading: isLoadingSlideItem, mutate: mutateSlideItems } = useMutation({
    mutationFn: updateSlideItems,
  });
  const { isLoading, mutate } = useMutation({
    mutationFn: updateLectureSegment,
  });
  const { isLoading: isLoadingRelease, mutate: mutateRelease } = useMutation({
    mutationFn: publicCourse,
  });

  const getIndexSection = (courseInfo: CourseDetailInfo) => {
    const defaultSections =
      courseInfo.sections?.filter((section) => section.sectionTypeId === SectionType.Default) ?? [];
    if (defaultSections.length === 0) return 1;
    else return defaultSections[defaultSections.length - 1]?.sortIndex + 1;
  };

  const { mutate: addChapter, isLoading: isAddingChapter } = useMutation({
    mutationFn: createSection,
    onSuccess: (data) => {
      setDisplay(Display.ChapterList);
      setIsShowChapter(true);
      notification.success({
        message: 'Thêm tiêu đề chương thành công',
      });
      refetch();

      setQuery({ ...query, sectionId: data.id });
      form.resetFields(['chapter']);
    },
    onError: (error: any) => {
      notification.error({
        message: error.response.data?.message,
      });
    },
  });

  const onFinish = (value: { chapter: string }) => {
    if (!courseInfo?.sections) return;

    const sort_index = getIndexSection(courseInfo);

    const payload: SectionCreateRequest = {
      course_id: courseInfo?.id?.toString() || '',
      section_name: value.chapter,
      sort_index,
      section_type_id: SectionType.Default,
    };
    addChapter(payload);
  };

  const handleSaveLectureDocument = () => {
    const body: UpdateLectureDocument = {
      data_update: lectureDocuments,
      lecture_id: parseInt(queryData?.lectureId as string),
    };
    updateLectureDocument(body)
      .then(() => {})
      .catch(() => {});
  };
  const handleSaveLectureInteraction = (isAutoSave: boolean, isPreview: boolean) => {
    let indexInvalid = -1;
    if (!isAutoSave) {
      const { index } = getIndexInvalidInteraction(lecturerInteracts);
      indexInvalid = index;
    }

    if (indexInvalid !== -1) {
      dispatch(setSelectedInteraction(lecturerInteracts[indexInvalid]));
      return false;
    }
    const lectureId = Number(queryData?.lectureId);
    const { lectureInteracts: lecture_interacts } = selectedLecture as any;
    const body: UpdateLectureBody = {
      lecture_interacts,
    };

    updateLectureInteraction(lectureId, body).then().catch();
    return true;
  };

  const isHasPermissionCreatSection = (): boolean => {
    if (!userIsVip(userInfo) && courseInfo?.sections && courseInfo.sections.length >= MAX_CREATE_FREE_COURSE) {
      notifyWarning('Tài khoản miễn phí chỉ được tạo tối đa 3 chương học. Nâng cấp để không giới hạn tạo chương học.');
      return false;
    }
    return true;
  };

  const onRedirectToPreviewLecture = () => {
    const currentLecture = getLectureInCourse(courseInfo!, queryData?.sectionId || '', queryData?.lectureId || '');
    const slideId = currentLecture?.slideId || queryData?.slideId;
    const { lectureTypeId } = currentLecture!;
    if (lectureTypeId === LectureType.Video) {
      handleSaveLectureAll({ isAutoSave: true, isPreview: true });
    }
    if (lectureTypeId === LectureType.Slide) {
      if (slideId) handleUpdateSlideItem(slideId, false, true);
    }
  };
  const onPreviewLecture = async () => {
    setIsLoadingPreview(true);
    try {
      const res = await startLearnCourse({ course_id: Number(courseInfo?.id || 0) });
      if (res.status === 200) {
        setIsLoadingPreview(false);
        onRedirectToPreviewLecture();
      }
    } catch {
      notification.warning({
        message: 'Vui lòng thử lại',
      });
      setIsLoadingPreview(false);
    }
  };

  const onRedirectPreview = () => {
    if (!courseInfo) return;

    router.push(
      queryString.stringifyUrl({
        url: routePaths.learner.path.replace(':courseId', courseId?.toString() as string),
        query: {
          lectureId: queryData?.lectureId,
          sectionId: queryData?.sectionId,
          slideId: queryData?.slideId,
          segment: queryData?.segment,
          mode: AppMode.Preview,
        },
      }),
    );
  };

  const handleSaveLectureAll = ({
    isAutoSave,
    isPreview,
    isPublishCourse,
  }: {
    isAutoSave: boolean;
    isPreview: boolean;
    isPublishCourse?: boolean;
  }) => {
    let isError = false;
    const newSegments = segmentsStore.map((segment: { segment_name?: any; start_at?: any; end_at?: any }) => {
      const { start_at, end_at } = segment;
      if (!segment.segment_name?.trim() && !isError && !isAutoSave && !isPreview) {
        isError = true;
        message.warning('Vui lòng điền tên cho các phân đoạn');
      }
      return {
        ...segment,
        start_at,
        end_at,
      };
    });

    if (!isAutoSave) {
      if (isError) {
        dispatch(setIsEmptySegmentName(true));
        return;
      } else {
        dispatch(setIsEmptySegmentName(false));
      }
    }

    const lecture_id = parseInt(queryData?.lectureId as string);
    const bodyLecture: UpdateLectureSegment = {
      // @ts-expect-error //TODO: fix type
      data_update: newSegments,
      lecture_id,
    };

    //validate & save lecture interaction
    if (!handleSaveLectureInteraction(isAutoSave, isPreview)) return;

    if (lecture_id !== 0) {
      // save lecture document
      handleSaveLectureDocument();

      // save lecture
      mutate(bodyLecture, {
        onSuccess: () => {
          if (isPublishCourse) {
            handleReleaseCourse();
          } else {
            onSuccessSave(isAutoSave, isPreview);
          }
        },
      });
    }
  };

  const onSuccessSave = (isAutoSave: boolean, isPreview: boolean) => {
    if (isPreview) {
      onRedirectPreview();
    }
    if (!isAutoSave) {
      notification.success({
        message: isPreview ? 'Đang chuyển sang chế độ xem trước bài học' : 'Cập nhật bài học thành công',
      });
      refetchChapter();
    }
  };

  const handleUpdateSlideItem = (
    slideId: string,
    isAutoSave: boolean,
    isPreview: boolean,
    isPublishCourse?: boolean,
  ) => {
    const cloneSide = [...slides];
    const newSlides = cloneSide.map((slide: SlideItem, index: number) => ({ ...slide, sort_index: index }));
    const body: UpdateSlides = {
      slide_id: slideId,
      data_update: newSlides as any,
    };

    const isValidSlide = newSlides.every((slideItem: SlideItem) => {
      if (slideItem.slide_item_type_id === InteractionType.Default) {
        return slideItem.slide_item_name;
      } else return true;
    });

    const indexInvalid = getIndexInvalidCaseStudy(cloneSide);
    if (indexInvalid !== -1) {
      dispatch(setSelectedSlideItem(cloneSide[indexInvalid]));
      return;
    }

    if (isValidSlide || isAutoSave || isPreview) {
      mutateSlideItems(body, {
        onSuccess: () => {
          if (isPublishCourse) {
            handleReleaseCourse();
          } else {
            onSuccessSave(isAutoSave, isPreview);
          }
        },
      });
    } else {
      notification.error({
        message: 'Vui lòng điền tên slide!',
      });
    }
  };

  const minCorrectAnswerNull = () => currentTest.min_correct_answer !== null;

  const doValidateTest = () => {
    let flag: boolean;
    flag = minCorrectAnswerNull();
    if (currentTest.has_limit_time && !currentTest.limit_time) {
      flag = false;
    }
    if (!currentTest.questions || currentTest.questions.length <= 0) {
      flag = false;
    }
    for (const question of currentTest.questions) {
      if (!question.question_name || !question.correct_answer?.length) {
        flag = false;
        break;
      }
      for (const question_option of question.question_options) {
        if (!question_option.option_name) {
          flag = false;
          break;
        }
      }
      if (!flag) break;
    }
    if (!flag) {
      dispatch(setTempSubmit(true));
    }
    return flag;
  };

  const handleSaveTest = (isAutoSave: boolean, isPublishCourse?: boolean) => {
    if (!doValidateTest() && !isAutoSave) {
      return notification.error({
        message: 'Vui lòng điền đầy đủ thông tin bài kiểm tra',
      });
    }

    const body = {
      ...currentTest,
      test_id: currentTest.id,
    };
    dispatch(setTempSubmit(false));

    updateTest(body)
      .then(() => {
        if (isAutoSave) return;
        if (isPublishCourse) {
          handleReleaseCourse();
          return;
        }
        refetchChapter();
        notification.success({
          message: 'Lưu bài kiểm tra thành công',
        });
      })
      .catch(() => {});
  };

  const handleClickCreateTest = () => {
    const body: CreateChapterBody = {
      course_id: Number(courseId),
      section_name: 'Bài kiểm tra',
      sort_index: courseInfo?.sections?.length!,
      section_type_id: SectionType.Test,
    };

    createChapter(body)
      .then((data) => {
        setDisplay(Display.ChapterList);
        setIsShowChapter(true);
        setQuery({ ...query, sectionId: data.data.id as any });
        form.resetFields(['chapter']);
        refetch();
        notification.success({
          message: 'Thêm mới bài kiểm tra thành công',
        });
      })
      .catch((error: any) => {
        notification.error({
          message: error.message,
        });
      });
  };

  const handleCreateSectionTest = async () => {
    const request = {
      course_id: courseId,
      section_name: 'Bài kiểm tra',
      sort_index: (courseInfo?.sections?.length || 0) + 1,
      section_type_id: SectionType.Test,
    } satisfies SectionCreateRequest;

    try {
      const data = await createSection(request);

      await createSectionTest({
        courseId: courseId,
        sectionId: data.id,
        payload: {
          ...omit(currentTest, 'test_id'),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      });
      setDisplay(Display.ChapterList);
      setIsShowChapter(true);
      setQuery({ ...query, sectionId: data.id });
      form.resetFields(['chapter']);
      refetch();
      notification.success({
        message: 'Thêm mới bài kiểm tra thành công',
      });
    } catch (error: unknown) {
      const errorMessage = (error as { message: string })?.message;
      notification.error({
        message: errorMessage,
      });
    }
  };

  const handleReleaseCourse = () => {
    const requestPayload: PublishCourseRequest = {
      courseId: courseId?.toString() || '',
    };
    mutateRelease(requestPayload, {
      onSuccess: async () => {
        await revalidatePath(routePaths.profile.children.creator.path);
        router.push(routePaths.profile.children.creator.path);
        notification.success({
          message: 'Xuất bản khóa học thành công, vui lòng đợi quản trị viên phê duyệt bài đăng',
        });
      },
      onError: (error: any) => {
        notification.error({
          message: error?.response?.data?.data,
        });
      },
    });
  };

  const handleSaveAllType = ({ isAutoSave, isPublishCourse }: { isAutoSave: boolean; isPublishCourse?: boolean }) => {
    const currentSection = getSectionInCourse(courseInfo as any, queryData?.sectionId as string);
    if (courseId && courseInfo.publish === CourseActive.Active) {
      draftCourse(Number(courseId));
    }
    if (currentSection?.section_type_id === SectionType.Test) {
      handleSaveTest(isAutoSave, isPublishCourse);
      return;
    } else if (
      currentSection?.section_type_id === SectionType.Target ||
      currentSection?.section_type_id === SectionType.Result
    ) {
      handleSaveTargetOrResult(isAutoSave, isPublishCourse);
      return;
    }
    if (!selectedLecture) {
      notification.error({
        message: 'Không tìm thấy bài học',
      });
      return;
    }
    handleSaveDefaultSection({ lecture: selectedLecture, isAutoSave, isPublishCourse });
  };

  const handleSaveDefaultSection = ({
    lecture,
    isAutoSave,
    isPublishCourse,
  }: {
    lecture: LectureDetail;
    isAutoSave: boolean;
    isPublishCourse?: boolean;
  }) => {
    const slideId = lecture?.slideId ?? queryData?.slideId;
    const { testId, lectureTypeId } = lecture;
    switch (lectureTypeId) {
      case LectureType.Video:
        handleSaveLectureAll({ isAutoSave: isAutoSave, isPreview: false, isPublishCourse });
        break;
      case LectureType.Slide:
        if (slideId) handleUpdateSlideItem(slideId, isAutoSave, false, isPublishCourse);
        break;
      case LectureType.Test:
        if (testId) {
          handleSaveTest(isAutoSave, isPublishCourse);
        }
        break;
    }
  };

  const onChangeRadio = (event: RadioChangeEvent) => {
    const { value } = event.target;
    setSequentially(value);
  };

  const isExitTestLecture = useMemo(() => checkTestLectureIsExit(courseInfo?.sections), [courseInfo]);

  const handleClickCreateTargetLearning = () => {
    const body: CreateTargetLearningBody = {
      course_id: Number(courseId),
      section_type_id: SectionType.Target,
    };
    createChapter(body)
      .then((data) => {
        setDisplay(Display.ChapterList);
        setIsShowChapter(true);
        setQuery({ ...query, sectionId: data.data.id as any });
        form.resetFields(['chapter']);
        refetch();
        notification.success({
          message: 'Thêm mới mục tiêu học tập thành công',
        });
      })
      .catch((error: any) => {
        notification.error({
          message: error.message,
        });
      });
  };

  const handleSaveTargetOrResult = (isAutoSave: boolean, isPublishCourse?: boolean) => {
    if (inValidStudyTarget(currentTarget?.learning_goal_content) && !isAutoSave) {
      message.warning('Tiêu đề và nội dung mục tiêu học tập không được để trống!');
      return;
    }

    const currentSection = getSectionInCourse(courseInfo as any, queryData?.sectionId!);
    let body: UpdateLearningGoalBody;
    if (currentSection?.section_type_id == SectionType.Target) {
      body = { ...currentTarget };
    } else {
      const refer_documents = currentStudyResult.refer_documents?.filter((document: string) => !!document);
      body = {
        ...currentStudyResult,
        refer_documents,
      };
    }
    updateLearningGoals(body)
      .then(() => {
        setDisplay(Display.ChapterList);
        setIsShowChapter(true);
        setQuery({ ...query });
        form.resetFields(['chapter']);
        if (isAutoSave) return;
        if (!isPublishCourse) {
          notification.success({
            message: 'Cập nhật thông tin thành công',
          });
          return;
        }
        handleReleaseCourse();
      })
      .catch((error: any) => {
        notification.error({
          message: error.message,
        });
      });
  };

  const validateBeforePublish = () => {
    const testSection = courseInfo.sections?.find((item) => item.sectionTypeId === SectionType.Test);
    if (testSection && !doValidateTest()) {
      notification.error({
        message: 'Bài kiểm tra cuối kỳ chưa đầy đủ thông tin, vui lòng kiểm tra lại',
      });
      return;
    }
    const targetSection = courseInfo.sections?.find((item) => item.sectionTypeId === SectionType.Target);
    if (targetSection && inValidStudyTarget(targetSection.learningGoal.learning_goal_content)) {
      notification.error({
        message: 'Mục tiêu học tập chưa đầy đủ thông tin, vui lòng kiểm tra lại',
      });
      return;
    }

    const resultSection = courseInfo.sections?.find((item) => item.sectionTypeId === SectionType.Result);
    if (resultSection && inValidStudyTarget(resultSection.learningGoal.learning_goal_content)) {
      notification.error({
        message: 'Kết quả học tập chưa đầy đủ thông tin, vui lòng kiểm tra lại',
      });
      return;
    }
    const defaultSection = courseInfo.sections?.filter((item) => item.sectionTypeId === SectionType.Default) || [];
    if (defaultSection.length <= 0) {
      notification.error({
        message: 'Khóa học chưa có chương học, vui lòng tạo chương học',
      });
      return;
    }
    let sectionHasNoLecture = '';
    for (let i = 0; i < defaultSection.length; i++) {
      if (defaultSection[i].lectures.length <= 0) {
        sectionHasNoLecture = !sectionHasNoLecture
          ? defaultSection[i].sectionName || ''
          : `${sectionHasNoLecture}, ${defaultSection[i].sectionName}`;
      }
    }
    if (sectionHasNoLecture) {
      notification.error({
        message: `${sectionHasNoLecture} chưa có bài học, vui lòng kiểm tra lại`,
      });
      return;
    }
    handleReleaseCourse();
  };

  return {
    form,
    isExitTestLecture,
    handleReleaseCourse,
    getCourseById,
    createChapter,
    updateLectureSegment,
    getChapterById,
    updateLectureDocument,
    updateSlideItems,
    createTarget,
    updateTest,
    currentStudyResult,
    dispatch,
    display,
    handleClickCreateTest,
    isLoading,
    isLoadingRelease,
    mutate,
    onChangeRadio,
    handleSaveAllType,
    isShowChapter,
    isLoadingSlideItem,
    queryData,
    // isShowSectionEditLayout,
    userInfo,
    sequentially,
    refetch,
    setDisplay,
    setIsShowChapter,
    onFinish,
    setIsShowModalDesignContent,
    isShowModalDesignContent,
    onPreviewLecture,
    onRedirectPreview,
    handleClickCreateTargetLearning,
    section,
    refetchChapter,
    isLoadingPreview,
    isAddingChapter,
    isHasPermissionCreatSection,
    validateBeforePublish,
    onPublishCourse: handleReleaseCourse,
    onCreateSectionTest: handleCreateSectionTest,
  };
}
