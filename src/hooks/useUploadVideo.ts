import { HTTP_STATUS_CODE } from '@/constants/api';
import { useCreate<PERSON>rouseApi } from '@/features/courses';

const CHUNK_SIZE = 800;

const useUploadVideo = () => {
  const { uploadChunkFile } = useCreateCrouseApi();

  const uploadVideoInChunk = async ({ userId, file }: { userId: string; file: File }) => {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    const fileName = file.name;

    let start = 0;
    for (let chunkIndex = 1; chunkIndex <= totalChunks; chunkIndex++) {
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);

      const formData = new FormData();
      formData.append('file', chunk);
      formData.append('chunk_number', chunkIndex.toString());
      formData.append('total_chunk', totalChunks.toString());
      formData.append('upload_key', fileName);
      formData.append('user_id', userId);

      try {
        const response = await uploadChunkFile(formData);
        if (response.status === HTTP_STATUS_CODE.CREATED) {
          start = end;
        }

        const isCompleted = response.data?.id;
        if (isCompleted) {
          return response.data as { fileUrl: string; id: string };
        }
      } catch (error) {
        console.error(`Error uploading chunk ${chunkIndex}:`, error);
        throw error;
      }
    }
  };

  return {
    uploadVideoInChunk,
  };
};

export default useUploadVideo;
