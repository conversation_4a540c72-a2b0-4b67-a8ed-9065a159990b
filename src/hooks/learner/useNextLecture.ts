import { CourseInfo, getSectionInCourse, LectureDetail, Section } from '@/features/courses';
import { redirectPath } from 'actions/redirectPath';
import { SectionType } from 'config';
import { isEmpty } from 'lodash-es';
import { useRouter } from 'next-nprogress-bar';
import { usePathname, useSearchParams } from 'next/navigation';
import queryString from 'query-string';
import { useCallback } from 'react';

interface UseNextLectureProps {
  sectionId: string;
  setIsReviewCourse?: (val: boolean) => void;
  isPreviewMode: boolean;
  selectedLecture: LectureDetail | null | undefined;
  courseInfo: CourseInfo | null | undefined;
}

export const useNextLecture = ({
  sectionId,
  setIsReviewCourse,
  isPreviewMode,
  selectedLecture,
  courseInfo,
}: UseNextLectureProps) => {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();

  const onClickLectureSidebar = ({
    lectureId,
    newSection,
    newSectionId,
  }: {
    lectureId: number | string | undefined;
    newSection?: Section | null;
    newSectionId?: number | null;
  }) => {
    if (!newSectionId) return;
    router.push(
      queryString.stringifyUrl({
        url: pathname,
        query: {
          ...queryString.parse(searchParams.toString()),
          lectureId,
          sectionId: newSection?.id ?? newSectionId ?? sectionId,
        },
      }),
    );
  };
  const getNextLectureInCurrentCourse = ({
    sectionId,
    currentLecture,
    currentCourse,
  }: {
    sectionId: string | null;
    currentLecture: LectureDetail | null | undefined;
    currentCourse: CourseInfo | undefined | null;
  }) => {
    if (!sectionId || !currentCourse || !Array.isArray(currentCourse?.sections)) return null;

    const currentSection = getSectionInCourse(currentCourse, sectionId);
    if (!currentSection) return null;

    const handleShowReviewCourse = (currentSection: Section) => {
      const { sectionTypeId, lectures } = currentSection;
      const currentIdx = lectures.findIndex((lecture) => lecture.id === currentLecture?.id);
      const canShow =
        [SectionType.Target, SectionType.Result, SectionType.Test].includes(sectionTypeId) ||
        currentIdx === lectures?.length;
      if (setIsReviewCourse) {
        setIsReviewCourse(canShow);
      }
    };

    const getNextLectureInSection = (currentLecture: LectureDetail | undefined | null) => {
      const { lectures } = currentSection;
      if (!currentLecture || !lectures) return null;
      const currentIdx = lectures.findIndex((lecture) => lecture.id === currentLecture.id);
      if (currentIdx !== -1) {
        return lectures[currentIdx + 1];
      }
      return null;
    };
    const getNextLectureInNextCourse = (sectionId: string, sections: Section[], count: number = 0): null | object => {
      if (count >= 40 || !sectionId) return null; // Giới hạn số lần đệ quy

      let result = { selectedSection: {}, selectedLecture: {} };
      const currentIdx = sections.findIndex((section: Section) => section.id === String(sectionId));
      const currentSection: Section = sections[currentIdx];

      if (currentSection) {
        const nextSection: Section = sections[currentIdx + 1] ?? {};
        const { sectionTypeId } = nextSection;
        const sectionId = nextSection?.id;

        if (isEmpty(nextSection)) {
          // Show đánh giá khóa học khi đang ở chương cuối cùng
          handleShowReviewCourse(currentSection);
        }

        switch (sectionTypeId) {
          case SectionType.Default:
            if (nextSection && nextSection.lectures.length > 0) {
              result = { selectedSection: {}, selectedLecture: nextSection.lectures[0] };
              break;
            }
            return getNextLectureInNextCourse(sectionId, sections, count + 1);

          case SectionType.Result:
          case SectionType.Target:
          case SectionType.Test:
            if (nextSection) {
              result = { selectedSection: nextSection, selectedLecture: {} };
              break;
            }

            return getNextLectureInNextCourse(sectionId, sections, count + 1);
        }
        return result;
      }
      return null;
    };

    const nextLecture = getNextLectureInSection(currentLecture);
    if (nextLecture) {
      return nextLecture;
    }

    const nextLectureInNextCourse = getNextLectureInNextCourse(sectionId, currentCourse.sections, 0);
    if (nextLectureInNextCourse) {
      return nextLectureInNextCourse;
    }
  };
  const handleNextLecture = useCallback(() => {
    if (isPreviewMode) return;

    const nextLecture: any =
      getNextLectureInCurrentCourse({
        sectionId,
        currentLecture: selectedLecture,
        currentCourse: courseInfo,
      }) ?? {};
    if (!isEmpty(nextLecture?.selectedLecture)) {
      onClickLectureSidebar({
        lectureId: nextLecture?.selectedLecture?.id,
        newSectionId: nextLecture?.selectedLecture?.section_id,
      });
      return;
    }
    if (!isEmpty(nextLecture?.selectedSection)) {
      onClickLectureSidebar({
        lectureId: undefined,
        newSectionId: nextLecture?.selectedSection?.id,
      });
    }
    if (nextLecture?.id && nextLecture?.section_id) {
      redirectPath(
        queryString.stringifyUrl({
          url: pathname,
          query: {
            lectureId: nextLecture?.id,
            sectionId: nextLecture?.section_id,
          },
        }),
      );
    }
  }, [courseInfo, isPreviewMode, sectionId, selectedLecture]);

  return { handleNextLecture, onClickLectureSidebar };
};
