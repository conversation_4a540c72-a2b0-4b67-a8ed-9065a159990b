import { LectureInteract } from '@/features/courses/types';
import { useState } from 'react';
import { timeStampToSecond } from 'utils';

export function useInteractionVideo() {
  const [interactionsVideo, setInteractionsVideo] = useState<LectureInteract[]>([]);
  const [selectedInteractionVideo, setSelectedInteractionVideo] = useState<LectureInteract | undefined>();

  const checkStartInteraction = (currentTime: number) => {
    for (let i = 0; i < interactionsVideo?.length; i++) {
      let flag = false;
      const interactionTime = timeStampToSecond(interactionsVideo[i].start_at);
      if (interactionTime > currentTime + 5) flag = false;
      if (flag || interactionsVideo[i].isShowed) continue;
      if (currentTime > interactionTime && currentTime <= interactionTime + 1) {
        setTimeout(() => {
          const cloneInteractionsVideo = [...interactionsVideo];
          cloneInteractionsVideo[i].isShowed = false;
          setInteractionsVideo(cloneInteractionsVideo);
          setSelectedInteractionVideo(interactionsVideo[i]);
        }, 500);
      }
    }
  };
  return {
    interactionsVideo,
    selectedInteractionVideo,
    checkStartInteraction,
    setInteractionsVideo,
    setSelectedInteractionVideo,
  };
}
