import HeaderGuest from 'components/header/HeaderGuest';
import <PERSON><PERSON><PERSON><PERSON> from 'components/header/HeaderLogged';
import { PropsWithChildren } from 'react';
import { userServices } from 'services/user.services';

// export async function generateMetadata(props: any, parent: ResolvingMetadata): Promise<Metadata> {
//   return metadataService().courseMetadata(props, parent);
// }

export function generateMetadata() {
  const metaData = { title: 'Chi tiết khóa học', description: 'Chi tiết khóa học' };
  return metaData;
}

export default async function CourseDetailRootPage({
  children,
}: Readonly<PropsWithChildren & { params: { id: string } }>) {
  const { isLoggedIn } = await userServices();
  let header = <HeaderGuest />;

  if (isLoggedIn) {
    header = <HeaderLogged />;
  }
  return (
    <>
      <div className={'border-b-1 sticky top-0 z-[20] h-[65px] w-full bg-white p-4 py-2'}>
        <div className={'m-auto flex w-full max-w-[1440px] items-center justify-between'}>{header}</div>
      </div>
      {children}
    </>
  );
}
