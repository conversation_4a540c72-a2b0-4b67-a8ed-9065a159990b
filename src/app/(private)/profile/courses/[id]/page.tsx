import NotFoundCourse from '@/components/not-found-course';
import { getCourseByIdForAuth } from '@/features/courses/services/server';
import { CourseDetail } from 'components/courses/courseDetail';
import { Metadata, ResolvingMetadata } from 'next';
import { metadataService } from 'services/metadata.service';
import { AppProps } from 'type/appProps';

export async function generateMetadata(props: AppProps, parent: ResolvingMetadata): Promise<Metadata> {
  return metadataService().courseMetadata(props, parent);
}

async function CourseDetailPage({
  params,
}: Readonly<{
  params: {
    id: string;
  };
}>) {
  const courseDetail = await getCourseByIdForAuth({ courseId: params.id });

  // TODO: need to check active and publish. This code is commented to integrated API smoothly
  // if (!courseDetail || courseDetail.active !== CourseActive.Active || courseDetail.publish !== CourseActive.Active) {
  //   return <NotFoundCourse />;
  // }

  if (!courseDetail) {
    return <NotFoundCourse />;
  }

  return <CourseDetail courseInfo={courseDetail} />;
}

export default CourseDetailPage;
