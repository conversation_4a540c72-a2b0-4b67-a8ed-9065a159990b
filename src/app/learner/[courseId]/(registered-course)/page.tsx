import {
  getCourseById,
  getCourseByIdForAuth,
  getLectureById,
  getSectionTestDetail,
} from '@/features/courses/services/server';
import { mapLectureDetail } from '@/features/courses/utils/mapper';
import { userServices } from '@/services/user.services';
import { Test } from '@/type';
import { LearnerHeader } from 'components/learner/components/header/LearnerHeader';
import { LearnerContainer } from 'components/learner/components/learnerContainer';
import { LearnerSectionName } from 'components/learner/components/learnerContainer/LearnerSectionName';
import { ResultContainer } from 'components/learner/components/resultContainer';
import { TargetContainer } from 'components/learner/components/targetContainer';
import { TestContainer } from 'components/learner/components/testContainer';
import { NotFoundCourse } from 'components/not-found-course';
import { SectionType, routePaths } from 'config/constant';
import { ChapterSidebarProvider } from 'context/ChapterSidebarProvider';
import { VideoProvider } from 'context/VideoProvider';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { AppProps } from 'type/appProps';

const ChapterDrawer = dynamic(() => import('components/learner/components/chapterDrawer'), {
  ssr: false,
});
export const metadata: Metadata = {
  title: 'Learner - Studify',
};
const FINISH_PERCENT = 80;

async function LearnerCoursePage({ searchParams, params: { courseId } }: Readonly<AppProps>) {
  const {
    sectionId,
    lectureId,
  }: {
    sectionId?: string;
    lectureId?: string;
  } = searchParams;
  // const courseOfUserPromise = courseServices().getCourseRegistered(courseId);
  // const sectionPromise = sectionService().detail(sectionId!);

  const { isLoggedIn } = await userServices();

  const courseDetail = isLoggedIn ? await getCourseByIdForAuth({ courseId }) : await getCourseById({ courseId });

  const section = courseDetail?.sections?.find((section) => section.id === sectionId);

  const testDetail = section?.test?.id
    ? ((await getSectionTestDetail({
        courseId: courseId.toString(),
        sectionId: sectionId || '',
        testId: section?.test?.id || '',
      })) as unknown as Test)
    : null;

  // if (!courseDetail || !section) return null;

  // const [courseOfUser, section] = await Promise.all([courseOfUserPromise, sectionPromise]);
  // if (!courseOfUser || !section) {
  //   return <NotFoundCourse />;
  // }

  let learnerSection = <></>;

  if (section?.sectionTypeId === SectionType.Test) {
    learnerSection = (
      <div className="wrap-playing wrap-playing-learner relative overflow-hidden">
        <TestContainer
          courseInfo={courseDetail!}
          lectureTestData={testDetail!}
          currentSection={section}
          selectedLecture={undefined}
        />
        <LearnerSectionName section={section!} lectureType={undefined} />
      </div>
    );
  }

  if (section?.sectionTypeId === SectionType.Target) {
    learnerSection = <TargetContainer courseInfo={courseDetail!} section={section} />;
  }

  if (section?.sectionTypeId === SectionType.Result) {
    learnerSection = <ResultContainer learnerCourse={courseDetail!} currentSection={section} />;
  }

  if (section?.sectionTypeId === SectionType.Default) {
    if (!lectureId) return <NotFoundCourse />;

    const lecture = await getLectureById({ courseId, sectionId: sectionId || '', lectureId });

    const currentLecture = mapLectureDetail(lecture);

    // const currentLectureCompleted = courseDetail?.userCompletedLectures?.find(
    //   (completedLecture) => completedLecture.lectureId.toString() === lectureId.toString(),
    // );

    // if (!currentLectureCompleted) {
    //   await fetchData(`${apiUrls.completedLecture}/${lectureId}`, {
    //     method: 'POST',
    //   });
    // }

    const completedPercentage = Math.round(
      ((courseDetail?.countCompletedLectures ?? 0) * 100) / (courseDetail?.totalLectures ?? 0),
    );

    const showReviewButton = () => {
      if (courseDetail?.current_user_rated_count && courseDetail.current_user_rated_count > 0) {
        return false;
      }
      return completedPercentage >= FINISH_PERCENT;
    };
    learnerSection = (
      <>
        {showReviewButton() && (
          <Link href={routePaths.learner.children.review.path.replace(':courseId', courseId)}>
            <button
              className={
                'absolute bottom-20 right-10 z-50 rounded-lg bg-primary px-6 py-3 text-white hover:bg-primary-800'
              }
            >
              Đánh giá khóa học
            </button>
          </Link>
        )}
        <VideoProvider>
          <LearnerContainer
            lectureTestData={testDetail!}
            isLearnerPreview={false}
            selectedLecture={currentLecture!}
            courseInfo={courseDetail!}
            currentSection={section}
          />
        </VideoProvider>
      </>
    );
  }

  return (
    <ChapterSidebarProvider>
      <div className={'video-section relative h-screen w-screen overflow-hidden'}>
        <LearnerHeader courseInfo={courseDetail!} />
        {learnerSection}
      </div>
      <ChapterDrawer courseInfo={courseDetail!} />
    </ChapterSidebarProvider>
  );
}

export default LearnerCoursePage;
