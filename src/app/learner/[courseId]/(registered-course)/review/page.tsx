import ReviewLearner from 'components/learner/components/review-learner/ReviewLearner';
import { Metadata } from 'next';
import { masterDataService } from 'services/masterData.service';
import { AppProps } from 'type/appProps';

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON> giá khóa học',
  description: '<PERSON><PERSON> lại một đánh giá khóa học',
};

export default async function ReviewRootPage({ params: { courseId } }: Readonly<AppProps<{ courseId: number }>>) {
  const masterData = await masterDataService().list({
    feelings: true,
  });

  return <ReviewLearner courseId={courseId} masterData={masterData} />;
}
