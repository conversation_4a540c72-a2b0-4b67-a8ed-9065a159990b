import { getLectureById } from '@/features/courses/services/server';
import { CourseInfo } from '@/features/courses/types';
import { LearnerHeader } from 'components/learner/components/header/LearnerHeader';
import { LearnerContainer } from 'components/learner/components/learnerContainer';
import { LearnerSectionName } from 'components/learner/components/learnerContainer/LearnerSectionName';
import { ResultContainer } from 'components/learner/components/resultContainer';
import { TargetContainer } from 'components/learner/components/targetContainer';
import { TestContainer } from 'components/learner/components/testContainer';
import { NotFoundCourse } from 'components/not-found-course';
import { ShortCourse } from 'components/short-course';
import { SectionType } from 'config/constant';
import { CourseType } from 'constants/enum';
import { ChapterSidebarProvider } from 'context/ChapterSidebarProvider';
import { VideoProvider } from 'context/VideoProvider';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { notFound } from 'next/navigation';
import { courseServices } from 'services/course.services';
import { userServices } from 'services/user.services';
import { AppProps } from 'type/appProps';

const ChapterDrawer = dynamic(() => import('components/learner/components/chapterDrawer'), {
  ssr: false,
});
export const metadata: Metadata = {
  title: 'Admin preview',
};

async function LearnerCoursePage({ searchParams, params: { courseId } }: Readonly<AppProps>) {
  const {
    sectionId,
    lectureId,
  }: {
    sectionId?: string;
    lectureId?: string;
  } = searchParams;
  const { adminToken } = await userServices();
  if (!adminToken) {
    notFound();
  }
  const course = (await courseServices().getCourseDetailByAdmin(courseId)) as unknown as CourseInfo;

  const section = course.sections?.[0];

  // if (!sectionId) {
  //   section = course.sections?.[0];
  // } else {
  //   section = await sectionService().detail(sectionId);
  // }

  if (!course || !section) {
    return <NotFoundCourse />;
  }
  if (course.courseTypeId === CourseType.Short) {
    return (
      <div className={'xl:px-0 m-auto flex h-screen w-full max-w-7xl flex-col items-center gap-6 px-3 py-12'}>
        <p className={'text-2xl'}>Khóa học ngắn</p>
        <p className={'text-2xl'}>{course.courseName}</p>
        <ShortCourse fileUrl={course.sections?.[0].lectures[0].video?.fileUrl ?? ''} />
      </div>
    );
  }
  const courseOfUser = { completedLectures: [], totalLectures: 0 } as unknown as CourseInfo;

  let learnerSection = <></>;

  if (section.sectionTypeId === SectionType.Test) {
    learnerSection = (
      <div className="wrap-playing wrap-playing-learner relative overflow-hidden">
        <TestContainer courseInfo={course} currentSection={section} selectedLecture={undefined} />
        <LearnerSectionName section={section} lectureType={undefined} />
      </div>
    );
  }

  if (section.sectionTypeId === SectionType.Target) {
    learnerSection = <TargetContainer courseInfo={course} section={section} />;
  }

  if (section.sectionTypeId === SectionType.Result) {
    learnerSection = <ResultContainer learnerCourse={courseOfUser} currentSection={section} />;
  }

  if (section.sectionTypeId === SectionType.Default) {
    // const lecture = await lectureService().detail(lectureId ?? section.lectures?.[0].id);

    const lecture = await getLectureById({ courseId, sectionId: sectionId || '', lectureId: lectureId || '' });

    learnerSection = (
      <VideoProvider>
        <LearnerContainer
          isLearnerPreview={false}
          selectedLecture={lecture || undefined}
          courseInfo={course}
          currentSection={section}
        />
      </VideoProvider>
    );
  }

  return (
    <ChapterSidebarProvider>
      <div className={'video-section relative h-screen w-screen overflow-hidden'}>
        <LearnerHeader courseInfo={courseOfUser} />
        {learnerSection}
      </div>
      <ChapterDrawer courseInfo={courseOfUser} />
    </ChapterSidebarProvider>
  );
}

export default LearnerCoursePage;
