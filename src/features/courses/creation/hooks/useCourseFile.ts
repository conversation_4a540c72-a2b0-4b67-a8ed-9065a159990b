import { HTTP_STATUS_CODE } from '@/constants/api';
import { FileType } from '@/constants/file';
import { QUERY_KEYS } from '@/constants/query-keys';
import { mapFileTypeIdToFileType } from '@/features/courses/utils';
import { RootState } from '@/store/store';
import { App } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { useSelector } from 'react-redux';
import { useCreateCrouseApi } from '.';
import { UploadedFile } from '../../types';

const parseFileData = (data: { list: UploadedFile[]; total: number } | undefined) => {
  return {
    list: data?.list || [],
    total: data?.total || 0,
  };
};
const CHUNK_SIZE = 800;

const useFileUploadActions = () => {
  const { notification } = App.useApp();

  const fileTypeId = useSelector((state: RootState) => state.common.fileType);
  const fileType = mapFileTypeIdToFileType(fileTypeId);

  const { getUploadedFiles, uploadFile, deleteUploadedFile, updateFileName, uploadChunkFile } = useCreateCrouseApi();

  const { data: videoRes, refetch: refetchVideo } = useQuery([QUERY_KEYS.VIDEOS], () =>
    getUploadedFiles({ fileTypes: 'VIDEO' }),
  );

  const { data: imageRes, refetch: refetchImage } = useQuery([QUERY_KEYS.IMAGES], () =>
    getUploadedFiles({ fileTypes: 'IMAGE' }),
  );

  const { data: audioRes, refetch: refetchAudio } = useQuery([QUERY_KEYS.AUDIOS], () =>
    getUploadedFiles({ fileTypes: 'AUDIO' }),
  );

  const videoData = parseFileData(videoRes);
  const imageData = parseFileData(imageRes);
  const audioData = parseFileData(audioRes);

  const refetchData = async () => {
    if (!fileType) return;

    const funcMapping = {
      [FileType.VIDEO]: refetchVideo,
      [FileType.IMAGE]: refetchImage,
      [FileType.AUDIO]: refetchAudio,
    } as unknown as Record<FileType, () => Promise<void>>;

    const fetchFunc = funcMapping[fileType];
    await fetchFunc();

    refetchAudio();
    refetchImage();
    refetchVideo();
  };

  const uploadFileMutation = useMutation({
    mutationFn: (variables: { payload: FormData }) => uploadFile(variables),
    onSuccess: async () => {
      notification.success({ message: 'Upload file thành công' });
      await refetchData();
    },
    onError: () => {
      notification.error({ message: 'Upload file thất bại' });
    },
  });

  const deleteFileMutation = useMutation({
    mutationFn: (variables: { id: string }) => deleteUploadedFile(variables.id),
    onSuccess: async () => {
      notification.success({ message: 'Xóa file thành công' });
      await refetchData();
    },
    onError: (error) => {
      const errorMessage = (error as { message: string })?.message;
      notification.error({ message: errorMessage || 'Xóa file thất bại' });
    },
  });

  const updateFileNameMutation = useMutation({
    mutationFn: (variables: { id: string; fileName: string }) =>
      updateFileName(variables.id, { fileName: variables.fileName }),
    onSuccess: async () => {
      notification.success({ message: 'Cập nhật tên file thành công' });
      await refetchData();
    },
    onError: () => {
      notification.error({ message: 'Cập nhật tên file thất bại' });
    },
  });

  const uploadVideoInChunk = async ({ userId, file }: { userId: string; file: File }) => {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    const fileName = file.name;

    let start = 0;
    for (let chunkIndex = 1; chunkIndex <= totalChunks; chunkIndex++) {
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);

      const formData = new FormData();
      formData.append('file', chunk);
      formData.append('chunk_number', chunkIndex.toString());
      formData.append('total_chunk', totalChunks.toString());
      formData.append('upload_key', fileName);
      formData.append('user_id', userId);

      try {
        const response = await uploadChunkFile(formData);
        if (response.status === HTTP_STATUS_CODE.CREATED) {
          start = end;
        }

        const isCompleted = response.data?.id;
        if (isCompleted) {
          return response.data;
        }
      } catch (error) {
        console.error(`Error uploading chunk ${chunkIndex}:`, error);
        throw error;
      }
    }
  };

  return {
    videoData,
    imageData,
    audioData,

    uploadFileMutation,
    deleteFileMutation,
    updateFileNameMutation,

    uploadVideoInChunk,

    refetchVideo,
    refetchImage,
    refetchAudio,
  };
};

const useCourseFile = () => {
  const { getUploadedFiles } = useCreateCrouseApi();

  const {
    audioData,
    imageData,
    videoData,

    deleteFileMutation,
    updateFileNameMutation,
    uploadFileMutation,
    uploadVideoInChunk,

    refetchVideo,
    refetchImage,
    refetchAudio,
  } = useFileUploadActions();

  const handleSearchFileName = async ({
    fileName,
    fileTypes,
    onSuccess,
  }: {
    fileName: string;
    fileTypes: FileType;
    onSuccess?: (res: { list: UploadedFile[]; total: number }) => void;
  }) => {
    const fileTypeData = fileTypes.toUpperCase() as 'VIDEO' | 'IMAGE' | 'AUDIO';

    try {
      const res = await getUploadedFiles({ fileTypes: fileTypeData, fileName });
      onSuccess?.(res);
    } catch (error) {
      console.error('error: ', error);
    }
  };

  return {
    videoData,
    imageData,
    audioData,

    uploadFileMutation,
    deleteFileMutation,
    updateFileNameMutation,

    onSearchFileName: handleSearchFileName,
    onUploadVideoInChunk: uploadVideoInChunk,

    refetchVideo,
    refetchImage,
    refetchAudio,
  };
};

export default useCourseFile;
