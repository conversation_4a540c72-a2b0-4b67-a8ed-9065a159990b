import { useCreate<PERSON><PERSON><PERSON><PERSON> } from '@/features/courses/creation/hooks';
import { CreateSectionTestRequest, DeleteQuestionTestRequest } from '@/features/courses/creation/types';
import { useRouter } from 'next/navigation';
import { useMutation } from 'react-query';

const useSectionTestMutation = () => {
  const { createSectionTest, deleteSectionTest } = useCreateCrouseApi();

  const router = useRouter();

  const { isLoading: isCreatingQuestionTest, mutate: handleCreateQuestionTest } = useMutation({
    mutationFn: (variables: CreateSectionTestRequest) => createSectionTest(variables),
    onSuccess: (res) => {
      router.refresh();
      return res;
    },
    onError: (error: unknown) => {
      console.error('error: ', error);
    },
  });

  const { isLoading: isDeletingQuestionTest, mutate: handleDeleteQuestionTest } = useMutation({
    mutationFn: (variables: DeleteQuestionTestRequest) => deleteSectionTest(variables),
    onSuccess: (res) => {
      router.refresh();
      return res;
    },
    onError: (error: unknown) => {
      console.error('error: ', error);
    },
  });

  return {
    isCreatingQuestionTest,
    isDeletingQuestionTest,

    onCreateQuestionTest: handleCreateQuestionTest,
    onDeleteQuestionTest: handleDeleteQuestionTest,
  };
};

export default useSectionTestMutation;
