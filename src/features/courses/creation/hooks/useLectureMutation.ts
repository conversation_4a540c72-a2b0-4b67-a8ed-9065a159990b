import { useCreate<PERSON>rouseApi } from '@/features/courses/creation/hooks';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { App } from 'antd';
import { usePathname, useRouter } from 'next/navigation';
import queryString from 'query-string';
import { useMutation } from 'react-query';
import { Lecture } from '../../types';
import { CreateSlideRequest, DeleteVideoInteractionRequest, LectureCreationRequest } from '../types';

const getLectureRequest = ({
  lectureTypeId,
  lectures,
  courseId,
  sectionId,
}: {
  lectureTypeId: number;
  lectures: Lecture[];
  courseId: string;
  sectionId: string;
}) => {
  const getNextSortIndex = () => {
    const baseIndex = lectures.length > 0 ? lectures[lectures.length - 1].sortIndex + 1 : 1;
    const existingLecture = lectures.find((lecture) => lecture.sortIndex === baseIndex);
    return existingLecture ? baseIndex + 1 : baseIndex;
  };

  const getUniqueLectureName = () => {
    const baseNumber = lectures.length + 1;
    const baseName = `Bài ${baseNumber}`;
    const exists = lectures.some((lecture) => lecture.lectureName === baseName);
    return exists ? `Bài ${baseNumber + 1}` : baseName;
  };

  return {
    lectureName: getUniqueLectureName(),
    sortIndex: getNextSortIndex(),
    lectureTypeId,
    courseId,
    sectionId,
  };
};

export const useLectureMutation = (options?: { onCreateLectureSuccess?: () => void }) => {
  const { onCreateLectureSuccess } = options || {};

  const { notification } = App.useApp();

  const {
    createLecture: createLectureService,
    createSlide,
    deleteVideoInteraction: deleteVideoInteractionService,
  } = useCreateCrouseApi();

  const { parsedQueryParams } = useSafeSearchParams<{
    courseId: string;
    sectionId: string;
  }>();

  const router = useRouter();

  const pathname = usePathname();

  const { mutate: handleCreateSlide } = useMutation({
    mutationFn: (variables: CreateSlideRequest) => createSlide(variables),
    onError: (error: unknown) => {
      console.error('error: ', error);
    },
  });

  const { mutate: handleCreateLecture } = useMutation({
    mutationFn: (variables: LectureCreationRequest) => createLectureService(variables),
    onSuccess: (lecture, variables) => {
      handleCreateSlide({
        courseId: variables.courseId,
        sectionId: variables.sectionId,
        lectureId: lecture.id,
        slideName: variables.lectureName,
      });

      router.push(
        queryString.stringifyUrl({
          url: pathname,
          query: { ...parsedQueryParams, lectureId: lecture.id },
        }),
      );

      router.refresh();
      onCreateLectureSuccess?.();
    },
    onError: () => {
      notification.error({
        message: 'Có lỗi trong quá trình tạo, vui lòng thử lại',
      });
    },
  });

  const { mutate: handleDeleteVideoInteraction } = useMutation({
    mutationFn: (variables: DeleteVideoInteractionRequest) => deleteVideoInteractionService(variables),
    onError: (error: unknown) => {
      console.error('error: ', error);
    },
  });

  return { handleCreateLecture, onDeleteVideoInteraction: handleDeleteVideoInteraction, getLectureRequest };
};
