import { CourseType } from '@/constants/enum';
import { transformImageUrlToFile } from '@/utils';
import { Form } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React from 'react';
import { CourseDetailInfo } from '../../types';
import { CREATE_COURSE_FIELDS } from '../constants/schema';
import { CreateCourseRequest } from '../types';

const getFormDataRequest = (values: CreateCourseRequest, file: File) => {
  const formData = new FormData();

  const fieldsToAppend = {
    // required fields
    [CREATE_COURSE_FIELDS.COURSE_THUMBNAIL_IMAGE]: file!,
    [CREATE_COURSE_FIELDS.COURSE_NAME]: values.course_name,
    [CREATE_COURSE_FIELDS.COURSE_DESCRIPTION]: values.course_description,
    [CREATE_COURSE_FIELDS.COURSE_TYPE_ID]: String(values.course_type_id),
    [CREATE_COURSE_FIELDS.TOPIC_ID]: values.topic_id,
    [CREATE_COURSE_FIELDS.TAG_IDS]: values.tag_ids,
    [CREATE_COURSE_FIELDS.PACKAGE_ID]: values.package_id,
    [CREATE_COURSE_FIELDS.IS_SEQUENTIAL]: String(values.is_sequential),
    [CREATE_COURSE_FIELDS.COURSE_LEVEL_ID]: String(values.course_level_id),

    // optional fields
    [CREATE_COURSE_FIELDS.COURSE_RELATED_IDS]:
      Array.isArray(values.course_related_ids) && values.course_related_ids.length > 0
        ? values.course_related_ids
        : null,

    [CREATE_COURSE_FIELDS.COURSE_FAQS]:
      Array.isArray(values.course_faqs) && values.course_faqs.length > 0 ? values.course_faqs : null,
  };

  Object.entries(fieldsToAppend).forEach(([key, value]) => {
    if (key === CREATE_COURSE_FIELDS.TAG_IDS && Array.isArray(value)) {
      (value as string[]).forEach((tagID) => {
        formData.append(key, tagID);
      });
    }

    if (key === CREATE_COURSE_FIELDS.COURSE_RELATED_IDS && Array.isArray(value)) {
      (value as string[]).forEach((courseID) => {
        formData.append(key, courseID);
      });
    }

    if (key === CREATE_COURSE_FIELDS.COURSE_FAQS && Array.isArray(value)) {
      (value as { id?: string; question: string; answer: string }[]).forEach((faq) => {
        formData.append(key, JSON.stringify(faq));
      });
    }

    if (
      value &&
      key !== CREATE_COURSE_FIELDS.COURSE_FAQS &&
      key !== CREATE_COURSE_FIELDS.TAG_IDS &&
      key !== CREATE_COURSE_FIELDS.COURSE_RELATED_IDS
    ) {
      formData.append(key, value as string);
    }
  });

  return formData;
};

const useCourseCreationForm = ({ courseInfo, isEdit = false }: { courseInfo: CourseDetailInfo; isEdit?: boolean }) => {
  const [form] = useForm();
  const [file, setFile] = React.useState<null | File>(null);

  const courseTypeWatched: CourseType =
    Number(Form.useWatch(CREATE_COURSE_FIELDS.COURSE_TYPE_ID, form)) ?? courseInfo.courseTypeId;

  const handleTransformUrlToFile = async (url: string) => {
    const file = await transformImageUrlToFile(url);
    if (file) {
      setFile(file);
      return file;
    }
  };

  React.useEffect(() => {
    if (isEdit) {
      const initForm = async () => {
        const transformedFile = await handleTransformUrlToFile(courseInfo.courseThumbnailImage);

        form.setFieldsValue({
          [CREATE_COURSE_FIELDS.COURSE_LEVEL_ID]: courseInfo?.courseLevelId,
          [CREATE_COURSE_FIELDS.COURSE_TYPE_ID]: courseInfo?.courseTypeId,
          [CREATE_COURSE_FIELDS.PACKAGE_ID]: courseInfo?.package?.id,
          [CREATE_COURSE_FIELDS.TOPIC_ID]: courseInfo?.topic?.id,
          [CREATE_COURSE_FIELDS.TAG_IDS]: courseInfo?.courseTag?.map(({ tag }) => tag.id),
          [CREATE_COURSE_FIELDS.IS_SEQUENTIAL]: courseInfo?.isSequential,
          [CREATE_COURSE_FIELDS.COURSE_RELATED_IDS]: courseInfo?.courseRelated?.map((course) => course.id),
          [CREATE_COURSE_FIELDS.COURSE_NAME]: courseInfo?.courseName,
          [CREATE_COURSE_FIELDS.COURSE_DESCRIPTION]: courseInfo?.courseDescription,
          [CREATE_COURSE_FIELDS.COURSE_THUMBNAIL_IMAGE]: transformedFile,
          [CREATE_COURSE_FIELDS.COURSE_FAQS]: courseInfo?.courseFaq?.map((faq) => ({
            question: faq.question,
            answer: faq.answer,
            id: faq.id,
          })),
        });
      };
      initForm();
    }
  }, [isEdit]);

  return {
    form,
    file,
    courseTypeWatched,

    setFile,
    getFormDataRequest,
  };
};

export default useCourseCreationForm;
