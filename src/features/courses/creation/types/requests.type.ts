import { CaseStudyItem, LectureInteract } from '@/features/courses/types';
import { QuestionOption } from '@/type';
import { ChartData, Explore, LayoutContent, Question, ShortClip, TableData } from '../../types/slide.type';

export type PublishCourseRequest = {
  courseId: string;
};

export type CreateCourseRequest = {
  userId: string;
  course_name: string;
  course_description: string;
  course_thumbnail_image: FormData;
  topic_id: string;
  course_type_id: number;
  tag_ids: string[];
  package_id: string;
  is_sequential: number;
  course_related_ids: string[];
  course_level_id: number;
  course_faqs: { id?: string; question: string; answer: string }[];
};

export type SectionCreateRequest = {
  course_id: string;
  section_name: string;
  sort_index: number;
  section_type_id: number;
};

export type SectionEditRequest = {
  sectionId: string;
  courseId: string;
  sectionName?: string;
  sort_index?: number;
};

export type SectionEditResponse = {
  id: string;
  createdAt: string;
  updatedAt: string;
};

export type LectureCreationRequest = {
  courseId: string;
  sectionId: string;
  lectureName: string;
  lectureTypeId: number;
  sortIndex: number;
};

export type UpdateLectureRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  payload: { fileId: string };
};

export type DeleteLectureRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
};

export interface QuestionRequest {
  id?: string; // This has id for update and no id for create
  question_type_id?: number;
  question_name: string;
  question_image: string;
  question_options: QuestionOption[] | null;
  correct_answer: number[];
  question_required: boolean;
  question_duration: number;
  reply_right_answer: string;
  reply_wrong_answer: string;
  background_color: string;
  created_by?: number | null;
  updated_by?: number | null;
  created_at?: string | null;
  updated_at?: string | null;
  video_url?: string | null;
}

export type SlideItemRequest = {
  id?: string; // This has id for update and no id for create
  chart_data: ChartData;
  updated_by: string | null;
  background_image: string;
  slide_item_type_id: number;
  slide_item_name: string;
  layout_type_id: number;
  layout_content: LayoutContent;
  background_color: string;
  table_data: TableData[];
  file_id: string | null;
  is_loop: number;
  sort_index: number;
  question: QuestionRequest | null;
  explore: Explore | null;
  case_study: CaseStudyItem | null;
  short_clip: ShortClip | null;

  sound?: string | null;
};

export type CreateSlideRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  slideName: string;
};

export type CreateSlideItemRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  slideId: string;
  payload: {
    data: SlideItemRequest[];
  };
};

export type DeleteSlideRequest = {
  lectureId: string;
  slideId: string;
  slideItemId: string;
};

export type CreateQuestionRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  payload: {
    lecture_interacts: LectureInteract[];
  };
};

export type DeleteVideoInteractionRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  interactId: string;
};

export type CreateSectionTestRequest = {
  courseId: string;
  sectionId: string;
  payload: {
    test_id?: string;
    test_name: string;
    min_correct_answer: number;
    has_limit_time: number | null;
    limit_time: number | null;
    created_at: string | null;
    updated_at: string | null;
    questions: Question[];
  };
};

export type DeleteQuestionTestRequest = {
  courseId: string;
  sectionId: string;
  testId: string;
  questionId: string;
};
