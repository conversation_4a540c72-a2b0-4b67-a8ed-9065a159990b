import { LectureInteract } from '@/features/courses/types';

export const CASE_STUDY_INITIAL_VALUE = {
  slide_id: 0,
  case_study_type_id: 0,
  slide_item_name: 'Tình huống',
  sort_index: 0,
  case_study_id: null,
  title: '<PERSON><PERSON><PERSON><PERSON> cứu tình huống',
  description: '',
  text_center_title: '',
  text_center_description: '',
  answers: [
    {
      name: '',
      answer: '',
      style_css: {
        textType: 'NOI_DUNG',
        font: {
          textAlign: 'left',
          fontFamily: 'Inter',
          fontWeight: 'normal',
          fontSize: 16,
          action: 0,
          color: '#ffffff',
        },
        background: {
          type: 'MAU_NEN',
          backgroundColor: '#ffffff',
          opacity: 25,
        },
      },
    },
    {
      name: '',
      answer: '',
      style_css: {
        textType: 'NOI_DUNG',
        font: {
          textAlign: 'left',
          fontFamily: 'Inter',
          fontWeight: 'normal',
          fontSize: 16,
          action: 0,
          color: '#ffffff',
        },
        background: {
          type: 'MAU_NEN',
          backgroundColor: '#ffffff',
          opacity: 25,
        },
      },
    },
    {
      name: 'Thêm trường hợp',
    },
  ],
  correct_answer: null,
  background_type: 0,
  background_color: '#000000',
  background_image: '',
};

export const lectureInteractionInitialValue = {
  interact_type_id: 2,
  interact_name: '',
  start_at: 0,
  duration: 5,
  lecture_id: '',
} satisfies LectureInteract;

export const lectureQuestionTestInitialValue = {
  sort_index: 1,
  question_type_id: 0,
  background_color: '#ffffff',
  question_name: '',
  question_required: true,
  question_duration: 0,
  reply_right_answer: '',
  reply_wrong_answer: '',
  question_image: '',
  correct_answer: [],
  video_url: '',
  question_options: [
    {
      option_index: 1,
      option_name: '',
      option_thumbnail_image: '',
    },
    {
      option_index: 2,
      option_name: '',
      option_thumbnail_image: '',
    },
  ],
};

export const lectureTestInitialValue = {
  test_name: '',
  min_correct_answer: 1,
  has_limit_time: null,
  limit_time: null,
  created_at: '',
  updated_at: '',
  questions: [],
};
