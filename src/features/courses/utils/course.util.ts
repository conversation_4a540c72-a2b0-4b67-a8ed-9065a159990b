import { InteractionType } from '@/constants';
import { FileType } from '@/constants/file';
import { LectureDetail, SlideItem } from '@/features/courses/types';
import { CaseStudyItem } from '@/type';
import { validateCaseStudy, validateExplore, validateQuestion } from '@/utils';
import { CourseInfo, Lecture, Section, UploadedFile } from '../types/course.type';

export enum SectionType {
  Default = 1,
  // TODO: double-check about number 2
  Test = 3,
  Target = 4,
  Result = 5,
}

export enum LectureType {
  Video = 1,
  Slide = 2,
  Test = 3,
  Target = 4,
  Result = 5,
}

export enum FileTypeId {
  Video = 1,
  Image = 2,
  Slide = 3,
  Audio = 4,
}

export const mapFileTypeToFileTypeId = (fileType: FileType): FileTypeId => {
  const map = {
    [FileType.VIDEO]: FileTypeId.Video,
    [FileType.IMAGE]: FileTypeId.Image,
    [FileType.SLIDE]: FileTypeId.Slide,
    [FileType.AUDIO]: FileTypeId.Audio,
  };

  return map[fileType] || FileTypeId.Video;
};

export const mapFileTypeIdToFileType = (fileTypeId: FileTypeId): FileType => {
  const map = {
    [FileTypeId.Video]: FileType.VIDEO,
    [FileTypeId.Image]: FileType.IMAGE,
    [FileTypeId.Slide]: FileType.SLIDE,
    [FileTypeId.Audio]: FileType.AUDIO,
  };

  return map[fileTypeId] || FileTypeId.Video;
};

export const sectionName = {
  [SectionType.Test]: 'Bài kiểm tra',
  [SectionType.Target]: 'Mục tiêu học tập',
  [SectionType.Result]: 'Kết quả học tập',
  [SectionType.Default]: 'Bài giảng video',
};

export const getSectionName = (currentChapter: Section | null) => {
  if (!currentChapter) return 'Bài giảng video';
  const sectionTypeId = Number(currentChapter?.sectionTypeId) as SectionType;

  return sectionName[sectionTypeId];
};

export const lectureName = {
  [LectureType.Test]: 'Bài kiểm tra',
  [LectureType.Target]: 'Mục tiêu học tập',
  [LectureType.Result]: 'Kết quả học tập',
  [LectureType.Video]: 'Bài giảng video',
  [LectureType.Slide]: 'Bài giảng slide',
};

export const getLectureName = (lecture: LectureDetail | Lecture | null) => {
  if (!lecture) return 'Bài giảng video';
  const lectureTypeId = lecture.lectureTypeId as LectureType;
  return lectureName[lectureTypeId];
};

export const getLectureInCourse = (courses: CourseInfo, sectionId: string, lectureId: string) => {
  if (courses && sectionId && lectureId) {
    const section: Section | undefined = getSectionInCourse(courses, sectionId);
    if (section) {
      return section.lectures.find((lecture) => Number(lecture.id) === parseInt(lectureId));
    }
  }
};

export const getSectionInCourse = (course: CourseInfo, sectionId: string | number) => {
  if (course) {
    const id = typeof sectionId === 'number' ? sectionId : parseInt(sectionId);
    return course.sections?.find((section) => Number(section.id) === id);
  }
};

export const checkTestLectureIsExit = (sections: Section[] | null | undefined) => {
  if (!sections) return false;
  return !!sections.find((section) => section.sectionTypeId === SectionType.Test);
};

export const getFileNameAudioById = (id: string | number | null | undefined, audios: UploadedFile[]) => {
  if (!id) return '';

  return audios.find((audio: UploadedFile) => audio.id === id)?.fileName;
};

export const getSlideInLecture = (lecture: Lecture | null | undefined, currentSlideIdx: number) => {
  if (lecture && (currentSlideIdx !== null || true)) {
    const slide_items = lecture?.slide?.slideItems || [];
    const selectedSlide: SlideItem | null = slide_items[currentSlideIdx];
    return slide_items.find((slideItem: SlideItem) => slideItem.id === selectedSlide.id);
  }
  return null;
};

export const getIndexInvalidCaseStudy = (slides: SlideItem[]) => {
  let index = -1;
  for (let j = 0; j < slides.length; j++) {
    const slide_item_type_id = slides[j].slide_item_type_id;
    const currentQuestion = slides[j]?.question;
    switch (slide_item_type_id) {
      case InteractionType.Question:
        if (currentQuestion && !validateQuestion(currentQuestion)) {
          index = j;
          break;
        }
        break;
      case InteractionType.Situation:
        if (!validateCaseStudy(slides[j]?.case_study as CaseStudyItem)) {
          index = j;
          break;
        }
        break;
      case InteractionType.Explore:
        if (!validateExplore(slides[j]?.explore?.description as string)) {
          index = j;
          break;
        }
        break;
    }
  }
  return index;
};
