import { LectureDetail, LectureInteract } from '@/features/courses/types';
import { PauseOutlined } from '@ant-design/icons';
import InteractionPoint from 'components/learner/components/interactionPoint/InteractionPoint';
import SoundVideo from 'components/learner/components/playVideo/PlayingVideo';
import ShowContentHyperNote from 'components/learner/components/showContentHyperNote/ShowContentHyperNote';
import { HyperNoteBody } from 'components/learner/type';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import { useVideoContext } from 'hooks/useVideoContext';
import AddNoteIcon from 'icons/AddNote';
import ChaptersIcon from 'icons/ChaptersIcon';
import CollapseIcon from 'icons/CollpaseIcon';
import ExpandIcon from 'icons/ExpendIcon';
import PlayIcon from 'icons/PlayIcon';
import RightArrowIcon from 'icons/RightArrowIcon';
import { VideoSeekSlider } from 'react-video-seek-slider';
import 'react-video-seek-slider/styles.css';
import screenfull from 'screenfull';
import { secondToHHMMSS } from 'utils/convert';
import './index.scss';

type ControlVideoProps = {
  handleShowNote: () => void;
  fullScreenRef: any;
  isFullScreen: boolean;
  currentSegmentName: string;
  isPreviewMode: boolean;
  selectedLecture: LectureDetail | undefined;
  hyperData: HyperNoteBody[];
  setSelectedInteractionVideo: (value: LectureInteract) => void;
  isLearnerPreview?: boolean;
};
export const ControlVideo = (props: ControlVideoProps) => {
  const {
    hyperData,
    isFullScreen,
    fullScreenRef,
    currentSegmentName,
    handleShowNote,
    selectedLecture,
    setSelectedInteractionVideo,
    isPreviewMode,
    isLearnerPreview,
  } = props;

  const {
    handleTimeChange,
    controlVideoRef,
    onHoverVideo,
    onFocusOutVideo,
    playerProgress,
    setPlayerProgress,
    backgroundControlRef,
  } = useVideoContext();
  const { currentTime, duration, volume, muted, playing, secondsLoaded } = playerProgress;
  const { handleOpenChapterDrawer } = useChapterDrawer();
  return (
    <>
      <button
        ref={backgroundControlRef}
        onMouseLeave={onFocusOutVideo}
        onMouseEnter={onHoverVideo}
        onClick={() => setPlayerProgress({ ...playerProgress, playing: !playing })}
        className={'absolute bottom-0 left-0 z-[1] w-full rounded-b-xl transition-all'}
        style={{
          height: 144,
          opacity: '1',
          backgroundImage:
            'url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAEiCAYAAAA4WrilAAAAAXNSR0IArs4c6QAAATRJREFUOE9tyPdHJ3AAh/Fve++9995d1957iySSRBJJIokkkRznJM45kkQS6Y/M0y8PH71/ePG8I5HvF8Ut0aTEkBJLBsTxSTwpCaQkkpJESjIZkMInqaSkkZJOSgYZkMknWaRkk5JDSi4ZkMcn+aQUkFJIShEZUMwnJaSUklJGSjkpFWRAJZ9UkVJNSg0ZUMsndaTUk9JASiMpTWRAM5+0kNJKShsp7WRAB590ktJFSjcZ8INPekj5SUovKX2k9JMBA3wySMoQKcOkjJABo3wyRso4KROkTJIBU3wyTcoMKbOkzJEB83yyQMoiKUukLJOyQn6xCmuwDhuwCVuwDTuwC3uwDwdwCEdwDCdwCmdwDhdwCVdwDTdwC7/gN/yBO7iHv/AP/sMDPMITPMMLvMIbvMPHJ4jOOXqlqTfgAAAAAElFTkSuQmCC")',
        }}
      />

      <div
        ref={controlVideoRef}
        onMouseLeave={onFocusOutVideo}
        onMouseEnter={onHoverVideo}
        style={{
          opacity: '1',
        }}
        className={
          'playing-control playing-control-learner absolute bottom-0 right-0 z-[1001] h-[53px] w-full transition-all'
        }
      >
        <div className={'absolute bottom-[48px] z-50 h-4 w-full'}>
          <VideoSeekSlider
            max={duration * 1000}
            currentTime={currentTime * 1000}
            bufferTime={secondsLoaded * 1000}
            onChange={(time) => handleTimeChange(time / 1000)}
            secondsPrefix="00:00:"
            minutesPrefix="00:"
          />
        </div>
        <ShowContentHyperNote
          isShowHyperNote={!isPreviewMode}
          hyperData={hyperData}
          totalTime={duration}
          type={'video'}
        />
        {!isLearnerPreview && (
          <InteractionPoint
            totalTime={duration}
            selectedLecture={selectedLecture}
            setSelectedInteractionVideo={(value) => {
              setPlayerProgress({ ...playerProgress, playing: false });
              setSelectedInteractionVideo(value);
            }}
            currentTime={currentTime}
          />
        )}

        <div className={'control flex h-full items-center justify-between px-4'}>
          <div className={'flex h-full w-[80%] items-center gap-8'}>
            <div className={'flex'}>
              <button
                onClick={() => {
                  setPlayerProgress({ ...playerProgress, playing: !playing });
                }}
              >
                {playing ? (
                  <span className={'text-[24px] text-white'}>
                    <PauseOutlined />{' '}
                  </span>
                ) : (
                  <PlayIcon />
                )}
              </button>
            </div>
            <SoundVideo
              volume={volume}
              setVolume={(volumeChange) => {
                setPlayerProgress({ ...playerProgress, volume: volumeChange });
              }}
              muted={muted}
              setMuted={(mutedValue) => {
                setPlayerProgress({ ...playerProgress, muted: mutedValue });
              }}
            />

            <span className={'inline-block min-w-[100px] text-white'}>{`${secondToHHMMSS(
              currentTime,
              'MMSS',
            )} / ${secondToHHMMSS(duration, 'MMSS')}`}</span>

            {currentSegmentName && (
              <div className={'flex items-center gap-1'}>
                <p className={'truncate text-sm text-white'}>
                  {currentSegmentName.length > 100 ? `${currentSegmentName.slice(0, 100)}...` : currentSegmentName}
                </p>
                <RightArrowIcon />
              </div>
            )}
          </div>

          <div className={'flex items-center gap-6'}>
            {!(isLearnerPreview || isPreviewMode || isFullScreen) && (
              <>
                <button title={'Ghi chú nhanh'} onClick={handleShowNote}>
                  <AddNoteIcon />
                </button>
                <button
                  title={'Xem danh sách chương'}
                  className={'flex cursor-pointer text-white'}
                  onClick={handleOpenChapterDrawer}
                >
                  <ChaptersIcon />
                </button>
              </>
            )}

            <button
              onClick={() => {
                screenfull.toggle(fullScreenRef.current);
              }}
            >
              {isFullScreen ? <CollapseIcon /> : <ExpandIcon />}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ControlVideo;
