'use client';
import { CourseDetailInfo } from '@/features/courses';
import { ChevronDownIcon, ChevronUpIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import SectionItem from 'components/courses/detail/SectionItem';
import { SectionType } from 'config';
import { useContext, useState } from 'react';
import { getTotalLectureInCourse } from 'utils/homepage/function';
import UserInfoContext from 'utils/providers/UserInfoProvider';

export type StandardCourseContentProps = {
  courseInfo: CourseDetailInfo;
};

export function StandardCourseContent(props: Readonly<StandardCourseContentProps>) {
  const { courseInfo } = props;

  const { isVipUser: isVip, isLoggedIn } = useContext(UserInfoContext);

  const idsOpen =
    courseInfo?.sections?.[0]?.sectionTypeId === SectionType.Target
      ? courseInfo?.sections?.[1]?.id
      : courseInfo?.sections?.[0]?.id;

  const [showMore, setShowMore] = useState(false);

  const handleShowMoreContent = () => setShowMore(!showMore);

  return (
    <div className="relative flex flex-col gap-4">
      <div className="flex w-full justify-between">
        <div className="font-semibold">Nội dung khóa học</div>
        <div className="text-sm text-ink-700">
          {`${getTotalLectureInCourse(courseInfo)} bài học `}(
          {(courseInfo.courseDuration ? Number(courseInfo.courseDuration) / 60 : 0).toFixed() ?? 0} phút)
        </div>
      </div>
      <div
        className={`flex flex-col gap-2 rounded-xl bg-neutral-50 p-6 ${
          showMore ? 'h-full' : 'max-h-[550px] pb-2'
        } overflow-hidden`}
      >
        {courseInfo?.sections?.map((section) => (
          <SectionItem
            key={section.id}
            section={section}
            activeIds={idsOpen?.toString() ? [idsOpen?.toString()] : []}
            courseInfo={courseInfo}
          />
        ))}
      </div>
      <ShowMoreContent
        isLoggedIn={isLoggedIn}
        showMoreContent={showMore}
        setShowMoreContent={handleShowMoreContent}
        isVip={isVip}
      />
    </div>
  );
}

export const ShowMoreContent = (props: {
  isVip?: boolean;
  isLoggedIn?: boolean;
  showMoreContent?: boolean;
  setShowMoreContent: () => void;
}) => {
  const { isVip, showMoreContent, isLoggedIn, setShowMoreContent } = props;
  let content = (
    <div className="bg-gradient-locked absolute bottom-0 w-full cursor-pointer pl-0 pt-6" onClick={setShowMoreContent}>
      <div className="flex w-full items-center justify-center gap-2 font-semibold text-primary">
        Xem thêm
        <ChevronDownIcon className={'h-6 w-6'} />
      </div>
    </div>
  );

  if (showMoreContent) {
    content = (
      <div
        className="flex w-full cursor-pointer items-center justify-center gap-2 font-semibold text-primary"
        onClick={setShowMoreContent}
      >
        Ẩn nội dung
        <ChevronUpIcon className={'h-6 w-6'} />
      </div>
    );
  }

  if (!isVip && isLoggedIn) {
    content = (
      <div className="bg-gradient-locked absolute bottom-0 w-full pl-0 pt-6">
        <div className="flex w-full items-center justify-center gap-2 font-semibold text-primary">
          <LockClosedIcon className={'h-6 w-6'} />
          Nâng cấp tài khoản để xem chi tiết
        </div>
      </div>
    );
  }

  if (!isLoggedIn) {
    content = (
      <div className="bg-gradient-locked absolute bottom-0 w-full pl-0 pt-6">
        <div className="flex w-full items-center justify-center gap-2 font-semibold text-primary">
          <LockClosedIcon className={'h-6 w-6'} />
          Đăng ký để xem danh sách bài học
        </div>
      </div>
    );
  }

  return <>{content}</>;
};

export default StandardCourseContent;
