'use client';

import { CourseDetailInfo, Lecture, Section } from '@/features/courses';
import { LikeOutlined } from '@ant-design/icons';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { Button, Space, notification } from 'antd';
import { SectionType, routePaths } from 'config';
import { MAX_LEARN_BASIC_COURSE } from 'constants/config';
import { CourseLevel } from 'constants/enum';
import { useLearner } from 'hooks/apis/learner/userCourse';
import StarBorderOutlined from 'icons/StarBorderOutlined';
import UserOutLined from 'icons/UserOutLined';
import { useRouter } from 'next-nprogress-bar';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import queryString from 'query-string';
import { useContext, useState } from 'react';
import { useMutation } from 'react-query';
import { IsCompletedCourse } from 'type';
import { notifyWarning } from 'utils/notify';
import UserInfoContext from 'utils/providers/UserInfoProvider';

export type LearnCourseProps = {
  courseInfo: CourseDetailInfo | undefined;
};
export const LearnCourse = ({ courseInfo }: LearnCourseProps) => {
  const { isVipUser, isLoggedIn, userInfo, setUserInfo } = useContext(UserInfoContext);

  const params = useParams();

  const { startLearnCourse } = useLearner();
  const [isLoading, setIsLoading] = useState(false);

  const courseLevelId = Number(courseInfo?.courseLevelId ?? 0);

  const onRedirectToLearner = (firstSection: Section, firstLecture?: Lecture) => {
    const courseId = params?.id?.toString();
    if (!courseId) {
      notification.warning({
        message: 'Không tìm thấy khóa học',
      });
      return;
    }

    const url = routePaths.learner.path.replace(':courseId', courseId);
    const queryParams = {
      lectureId: firstLecture?.id?.toString(),
      sectionId: firstSection.id?.toString(),
    };

    const fullUrl = queryString.stringifyUrl({ url, query: queryParams });

    // router.push(fullUrl);
    window.location.href = fullUrl; // TODO: remove this line after fix router.push
  };

  const isAcceptRegisterAndLearnCourse = () => {
    if (!userInfo || !courseInfo) {
      return false;
    }
    if (isVipUser) {
      if (courseInfo.userCourse) {
        return true;
      }
      setUserInfo({
        token: userInfo.token,
        info: {
          ...userInfo.info,
          total_course_pro_learned:
            courseLevelId === CourseLevel.Professional
              ? userInfo.info.total_course_pro_learned + 1
              : userInfo.info.total_course_pro_learned,
          total_course_basic_learned:
            courseLevelId === CourseLevel.Basic
              ? userInfo.info.total_course_basic_learned + 1
              : userInfo.info.total_course_basic_learned,
          total_course_cert_learned:
            courseLevelId === CourseLevel.Certificate
              ? userInfo.info.total_course_cert_learned + 1
              : userInfo.info.total_course_cert_learned,
        },
      });
      return true;
    }
    if (userInfo.info.total_course_basic_learned >= MAX_LEARN_BASIC_COURSE) {
      notifyWarning(
        'Tài khoản miễn phí chỉ cho phép học tối đa 3 Khóa học cơ bản. Nâng cấp khóa học để không giới hạn khóa học.',
      );
      return false;
    }
    if (
      userInfo.info.total_course_pro_learned >= MAX_LEARN_BASIC_COURSE ||
      userInfo.info.total_course_cert_learned >= MAX_LEARN_BASIC_COURSE
    ) {
      notifyWarning(
        'Tài khoản miễn phí chỉ cho phép học tối đa 3 Khóa học chuyên nghiệp. Nâng cấp khóa học để không giới hạn khóa học.',
      );
      return false;
    }
    return true;
  };
  const router = useRouter();

  const { mutate: handleLearnCourse } = useMutation({
    mutationFn: startLearnCourse,
  });
  const onStartLearn = async () => {
    if (!courseInfo) return;

    const userRegisteredCourse = courseInfo.userCourse !== null;
    const isAccept = isAcceptRegisterAndLearnCourse();
    if (!isAccept && !userRegisteredCourse) {
      return;
    }

    setIsLoading(true);

    const sectionsByDefault = courseInfo.sections?.filter((section) => section.sectionTypeId === SectionType.Default);
    const firstSection = sectionsByDefault?.[0];

    if (!firstSection) {
      notification.warning({
        message: 'Khóa học chưa có chương học và bài học',
      });
      setIsLoading(false);
      return;
    }

    const firstLecture = firstSection?.lectures[0];
    if (!firstLecture && firstSection.lectures.length === 0 && firstSection.sectionTypeId === SectionType.Default) {
      notification.warning({
        message: 'Khóa học chưa có bài học',
      });
      setIsLoading(false);
      return;
    }
    const courseId = params?.id?.toString()?.split('-').pop();
    if (!courseId) {
      notification.warning({
        message: 'Không tìm thấy khóa học',
      });
      return;
    }

    onRedirectToLearner(firstSection, firstLecture);

    setIsLoading(false);
    // handleLearnCourse(
    //   { course_id: parseInt(courseId) },
    //   {
    //     onSuccess: () => {
    //       onRedirectToLearner(firstSection, firstLecture);
    //     },
    //     onError: (error) => {
    //       setIsLoading(false);
    //       notification.warning({
    //         message: 'Tài khoản của bạn không đủ điền kiện học, vui lòng nâng cấp tài khoản',
    //       });
    //       console.error('error: ', error);
    //     },
    //   },
    // );
  };

  const onGoToRegisterPage = () => {
    router.push(routePaths.register);
  };

  const renderButtonLearner = () => {
    if (!isLoggedIn) {
      return 'Đăng ký';
    }
    if (!courseInfo?.userCourse) {
      return 'Học ngay';
    }

    if (courseInfo.userCourse.isCompleted === IsCompletedCourse.Completed) {
      return 'Học lại';
    }

    return 'Tiếp tục học';
  };

  return (
    <>
      <div className="grid w-full grid-cols-3 gap-2 text-xs text-ink-black">
        <Space className="rounded-md bg-neutral-50 p-2" direction={'vertical'} align={'center'}>
          <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M17.7878 7.2959H21.4241C21.4663 7.2959 21.5068 7.31266 21.5366 7.3425C21.5664 7.37233 21.5832 7.41279 21.5832 7.45499V21.0914C21.5832 21.1335 21.5664 21.174 21.5366 21.2038C21.5068 21.2337 21.4663 21.2504 21.4241 21.2504H17.7878C17.7456 21.2504 17.7051 21.2337 17.6753 21.2038C17.6454 21.174 17.6287 21.1335 17.6287 21.0914V7.45499C17.6287 7.41279 17.6454 7.37233 17.6753 7.3425C17.7051 7.31266 17.7456 7.2959 17.7878 7.2959Z"
              stroke="#0F1013"
              strokeWidth="1.5"
            />
            <path
              d="M10.515 2.75H14.1514C14.1936 2.75 14.2341 2.76676 14.2639 2.7966C14.2937 2.82643 14.3105 2.8669 14.3105 2.90909V21.0909C14.3105 21.1331 14.2937 21.1736 14.2639 21.2034C14.2341 21.2332 14.1936 21.25 14.1514 21.25H10.515C10.4729 21.25 10.4324 21.2332 10.4026 21.2034C10.3727 21.1736 10.356 21.1331 10.356 21.0909V2.90909C10.356 2.8669 10.3727 2.82643 10.4026 2.7966C10.4324 2.76676 10.4729 2.75 10.515 2.75Z"
              fill="#FFC41D"
              stroke="#0F1013"
              strokeWidth="1.5"
            />
            <path
              d="M7.0378 12.9091V21.0909C7.0378 21.1331 7.02104 21.1736 6.9912 21.2034C6.96137 21.2332 6.9209 21.25 6.87871 21.25H3.24234C3.20015 21.25 3.15968 21.2332 3.12985 21.2034C3.10001 21.1736 3.08325 21.1331 3.08325 21.0909V12.9091C3.08325 12.8669 3.10001 12.8264 3.12985 12.7966C3.15968 12.7668 3.20015 12.75 3.24234 12.75H6.87871C6.9209 12.75 6.96136 12.7668 6.9912 12.7966C7.02104 12.8264 7.0378 12.8669 7.0378 12.9091Z"
              stroke="#0F1013"
              strokeWidth="1.5"
            />
          </svg>
          <div className="text-ink-700">Trung bình</div>
        </Space>
        <Space className="rounded-md bg-neutral-50 p-2" direction={'vertical'} size={4} align={'center'}>
          <Space>
            <UserOutLined />
            <div className={'font-semibold'}>{courseInfo?.totalLearner}</div>
          </Space>
          <div className="text-ink-700">Người học</div>
        </Space>
        <Space className="rounded-md bg-neutral-50 p-2" direction={'vertical'} align={'center'}>
          <Space align={'center'} className="items-center">
            <LikeOutlined style={{ fontSize: 24 }} />
            <div className="font-semibold">{courseInfo?.totalRating}</div>
          </Space>
          <div className="text-ink-700">Tích cực</div>
        </Space>
      </div>
      <div className="pb-12 pt-6 text-sm text-ink-black">
        <Space>
          <StarBorderOutlined fill={'black'} width={12} height={12} />
          <span className="text-ink-black">Học trên mọi thiết bị điện tử</span>
        </Space>
        <Space>
          <StarBorderOutlined fill={'black'} width={12} height={12} />
          Thời gian học linh hoạt
        </Space>
        <Space>
          <StarBorderOutlined fill={'black'} width={12} height={12} />
          Truy cập không giới hạn
        </Space>
      </div>
      <div className="absolute bottom-0 left-0 w-full">
        <Link href={!isLoggedIn ? routePaths.register : ''}>
          <Button
            loading={isLoading}
            type={'primary'}
            className="w-full"
            style={{ borderTopLeftRadius: 0, borderTopRightRadius: 0 }}
            size={'large'}
            onClick={() => (isLoggedIn ? onStartLearn() : onGoToRegisterPage())}
          >
            <Space>
              <span>{renderButtonLearner()}</span>
              <div className="w-6">
                <ArrowRightIcon />
              </div>
            </Space>
          </Button>
        </Link>
      </div>
    </>
  );
};

export default LearnCourse;
