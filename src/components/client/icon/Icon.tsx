import { cn } from '@/lib/utils';
import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

interface IconProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  icon: React.ReactNode;
}

const Icon: React.FC<IconProps> = ({ icon, size = 'md', className }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const renderIcon = () => {
    if (typeof icon === 'string') {
      return (
        <Image src={icon} alt={'icon'} width={24} height={24} className={clsx(cn(sizeClasses[size]), className)} />
      );
    }

    return icon;
  };

  return <div className={cn(sizeClasses[size], className)}>{renderIcon()}</div>;
};

export default Icon;
