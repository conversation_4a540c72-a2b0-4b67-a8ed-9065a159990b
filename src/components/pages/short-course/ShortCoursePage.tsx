import { getCourseById, getCoursesOfCreator } from '@/features/courses/services/server';
import Footer from 'components/Footer';
import BreadCrumb from 'components/courses/detail/BreadCrumb';
import BreadCrumbPublic from 'components/courses/detail/BreadCrumbPublic';
import Description from 'components/courses/detail/Description';
import CourseOtherList from 'components/courses/detail/OtherList';
import CourseRelatedList from 'components/courses/detail/RelatedList';
import ViewMore from 'components/home/<USER>';
import { NotFoundCourse } from 'components/not-found-course';
import { ShortCourse } from 'components/short-course';
import { routePaths } from 'config';
import querystring from 'query-string';
import { userServices } from 'services/user.services';
import { CourseActive } from 'type';
import { AppProps } from 'type/appProps';

export async function ShortCourseDetailPage({ params }: Readonly<AppProps>) {
  const { isLoggedIn } = await userServices();

  const course = await getCourseById(params.id);

  const { data: relatedCourses } = await getCoursesOfCreator({ userId: course?.createdBy.id || '', limit: 4 });

  if (!course || course.active !== CourseActive.Active) {
    return <NotFoundCourse />;
  }

  return (
    <>
      <BreadCrumbPublic
        breadCrumb={
          <BreadCrumb
            prePageUrl={
              isLoggedIn ? routePaths.profile.children.course.children.discovery.path : routePaths.course.index
            }
            categoryCourseHref={
              isLoggedIn
                ? querystring.stringifyUrl({
                    url: routePaths.profile.children.course.children.discovery.path,
                    query: {
                      topic_id: course.topic.id,
                    },
                  })
                : querystring.stringifyUrl({
                    url: routePaths.course.index,
                    query: {
                      topic_id: course.topic.id,
                    },
                  })
            }
            prevPageTitle={'Khóa học'}
            categoryCourse={course.topic?.topicName}
            courseTitle={course.courseName ?? ''}
          />
        }
      />
      <div className={'m-auto max-w-[calc(100vw-220px)]'}>
        <Description
          courseInfo={course}
          // className={'xl:pl-[84px]'}
          showMoreContent
          fillSvg={'#FFC41D'}
        />
      </div>
      <div className={'min-[1200px] m-auto max-w-7xl px-8 pt-6'}>
        <ShortCourse fileUrl={course.sections?.[0].lectures[0].video?.fileUrl ?? ''} />
        <div className={'mt-6'}>
          <CourseOtherList author={course.createdBy} />
        </div>
        <CourseRelatedList relatedCourses={relatedCourses} />
      </div>
      <ViewMore
        title={`Bạn muốn tìm hiểu về Studify? <br /> Đăng ký ngay`}
        note={`Đăng ký tài khoản để tham gia cộng đồng học tập <br /> và trải nghiệm các tính năng tương tác mới lạ với bài
            học ngay trên Studify`}
        textBtn={'Đăng ký Studify'}
      />
      <Footer />
    </>
  );
}

export default ShortCourseDetailPage;
