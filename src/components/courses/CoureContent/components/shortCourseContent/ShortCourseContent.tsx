'use client';
import { Upload } from '@/components/ui';
import { CourseDetailInfo } from '@/features/courses';
import { validateVideoFile } from '@/lib/helpers/fileValidation';
import { LoadingOutlined } from '@ant-design/icons';
import { Button, notification, Spin } from 'antd';
import Modal from 'antd/es/modal/Modal';
import { Video } from 'components/common/Video';
import { ACCEPT_VIDEO_FILE_TYPE } from 'constants/config';
import { useCourse } from 'hooks/apis/course';
import { DownloadFile } from 'icons';
import { useRouter } from 'next-nprogress-bar';
import { useMemo, useState } from 'react';
import { useMutation } from 'react-query';
import { initLecture } from 'reducer/courseReducer/initValueState';
import { UploadVideoForShortCourse } from 'type';
import { notifyError, notifySuccess } from 'utils';

const ShortCourseContent = (
  // props: CourseContentProps
  {
    courseInfo,
  }: {
    courseInfo: CourseDetailInfo;
  },
) => {
  const { uploadVideoForShortCourse, deleteLecture } = useCourse();
  const [deleteLectureId, setDeleteLectureId] = useState<number | null>(null);

  const { mutate: uploadVideoMutate, isLoading } = useMutation((body: UploadVideoForShortCourse) =>
    uploadVideoForShortCourse(body),
  );
  const { mutate: deleteVideoMutate } = useMutation((lectureId: number) => deleteLecture(lectureId));
  const { video, lecture } = useMemo(() => {
    const lecture = courseInfo?.sections?.[0]?.lectures.find((lecture) => lecture.videoId !== null) ?? initLecture;
    const video = lecture.video;
    return {
      video,
      lecture,
    };
  }, [courseInfo?.sections]);

  const router = useRouter();

  const handleDeleteCourse = (lectureId: number) => {
    deleteVideoMutate(lectureId, {
      onSuccess: () => {
        router.refresh();
        notifySuccess('Xóa video khóa học thành công');
      },
      onError: () => notifyError('Xóa video khóa học thất bại'),
    });
  };

  const uploadVideoToLecture = (file: File) => {
    const body: UploadVideoForShortCourse = {
      video: file,
      section_id: Number(courseInfo?.sections?.[0]?.id ?? '1'),
      sort_index: 1,
    };
    uploadVideoMutate(body, {
      onSuccess: () => {
        router.refresh();
        notifySuccess('Thêm video cho khóa học thành công');
      },
      onError: () => notifyError('Thêm video cho khóa học thất bại'),
    });
  };

  const handleChangeFile = (file: File) => {
    uploadVideoToLecture(file);
  };

  const handleBeforeUpload = (file: File) => {
    const { isValid, error: message } = validateVideoFile(file);
    if (!isValid) {
      notification.warning({ message });
      return false;
    }

    return true;
  };

  const uploadFileNow = () => (
    <Spin
      spinning={isLoading}
      tip={<p className={'mt-6 font-medium text-primary'}>Đang tải video, vui lòng chờ trong giây lát...</p>}
      indicator={<LoadingOutlined style={{ fontSize: 40, fontWeight: 'bold' }} spin />}
    >
      <>
        <p className={'mb-5 font-bold'}>Tải video nội dung</p>
        <Upload
          showUploadList={false}
          accept={ACCEPT_VIDEO_FILE_TYPE}
          beforeUpload={handleBeforeUpload}
          onChange={(info) => {
            if (info.file.originFileObj) {
              handleChangeFile(info.file.originFileObj);
            }
          }}
        >
          <div
            className={
              'relative flex h-[500px] w-full cursor-pointer items-center justify-center rounded-[8px] bg-[#f2f2f4]'
            }
          >
            <div className={'w-[425px]'}>
              <div className={'mx-auto mb-[38px] text-center'}>
                <div className={'flex justify-center pt-[45px]'}>
                  <DownloadFile />
                </div>
                <p className={'text-base'}>Kéo và thả video của bạn, hoặc nhấn vào đây để chọn</p>
                <p className={'text-base text-[#999]'}>(Dung lượng tối đa 200MB)</p>
              </div>
              <div className={''}>
                <ul className={'flex list-disc justify-between px-[25px] text-base text-[#999]'}>
                  <li>Tỉ lệ video 16:9</li>
                  <li>Kích thước khuyến nghị 1024x576</li>
                </ul>
              </div>
            </div>
          </div>
        </Upload>
      </>
    </Spin>
  );

  return (
    <div className={'center-x-y h-[calc(100vh-72px)] w-screen'}>
      <div className={`${video?.fileUrl ? 'bg-black' : ''} h-[80vh] w-[75vw]`}>
        {video?.fileUrl ? (
          <Video urlFile={video?.fileUrl} showDelete onDelete={() => setDeleteLectureId(Number(lecture.id))} />
        ) : (
          uploadFileNow()
        )}
      </div>

      <Modal
        onCancel={() => setDeleteLectureId(null)}
        title="Xóa video bài học"
        open={!!deleteLectureId}
        footer={[
          <Button
            key={'cancel'}
            onClick={() => {
              setDeleteLectureId(null);
            }}
            className={'w-[110px]'}
          >
            Huỷ
          </Button>,
          <Button
            key={'submit'}
            type="primary"
            loading={isLoading}
            onClick={() => {
              setDeleteLectureId(null);
              handleDeleteCourse(deleteLectureId as number);
            }}
            className={'w-[110px] !bg-orange-600'}
          >
            Xoá
          </Button>,
        ]}
      >
        <p>Bạn có chắc chắn xóa video này không? </p>
        <p>Bạn không thể phục hồi lại video này sau khi đã xóa.</p>
      </Modal>
    </div>
  );
};

export default ShortCourseContent;
