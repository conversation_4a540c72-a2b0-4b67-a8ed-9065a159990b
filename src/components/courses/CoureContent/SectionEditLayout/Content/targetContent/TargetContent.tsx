import { CourseInfo, Section } from '@/features/courses';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import TargetCollapse from 'components/courses/CoureContent/SectionEditLayout/Content/targetCollapses/TargetCollapse';
import { notEmpty } from 'config/constant';
import { HasAutoSwitch } from 'constants/enum';
import { useQuerySearch } from 'hooks';
import { PlayIcon, TargetImage } from 'images';
import { isNil } from 'lodash-es';
import Image from 'next/image';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setCurrentTargetForStore, setTargetsToStore } from 'reducer/courseReducer/targetSlice';
import { RootState } from 'store/store';
import { TargetItem } from 'type/course/target';
import { getIndexSectionInCourse } from 'utils/convert';
import './index.scss';

type TargetContentProps = {
  section: Section | undefined;
  courseInfo: CourseInfo | undefined;
};
const TargetContent = ({ section, courseInfo }: TargetContentProps) => {
  const queryData = useQuerySearch();
  const dispatch = useDispatch();

  const [form] = useForm();
  const targetStore = useSelector((state: RootState) => state.target.targetStore);
  const { time_auto_switch } = targetStore;
  const [isShowAddButton, setIsShowAddButton] = useState<boolean>(true);

  const [currentTarget, setCurrentTarget] = useState<TargetItem>({ title: '', description: '' });

  useEffect(() => {
    if (section?.id) {
      const { time_auto_switch, has_auto_switch, learning_goal_content } = section.learningGoal;
      dispatch(
        setCurrentTargetForStore({
          ...section.learningGoal,
          time_auto_switch: time_auto_switch || 15,
          has_auto_switch: isNil(has_auto_switch) ? HasAutoSwitch.ON : has_auto_switch,
          learning_goal_content: learning_goal_content || [],
        } as any),
      );
    }
  }, [section]);

  const handleAddTarget = () => {
    setIsShowAddButton(false);
  };

  const resetTarget = () => {
    setIsShowAddButton(true);
    setCurrentTarget({ title: '', description: '' });
  };
  const onFinish = (target: TargetItem) => {
    dispatch(
      setTargetsToStore([
        ...targetStore.learning_goal_content,
        {
          ...target,
          sort_index: target.sort_index ? target.sort_index : targetStore.learning_goal_content.length,
        },
      ]),
    );
    resetTarget();
  };

  const getNextSection = useMemo(() => {
    const currentSection = getIndexSectionInCourse(courseInfo as any, queryData?.sectionId);
    return courseInfo?.sections?.[currentSection + 1]?.sectionName;
  }, [courseInfo, queryData?.sectionId]);

  useEffect(() => {
    form.setFieldsValue(currentTarget);
  }, [currentTarget]);

  const handleSetTargets = (targets: TargetItem[]) => {
    dispatch(setTargetsToStore(targets));
  };

  const renderTextEditor = () => {
    if (targetStore?.learning_goal_content?.length > 4) return;
    return (
      <Form onFinish={onFinish} layout={'vertical'} form={form}>
        <div className={'mt-2 rounded-sm bg-white p-[12px]'}>
          <Form.Item
            label={'Tiêu đề*'}
            name={'title'}
            rules={[
              {
                required: true,
                message: `Tiêu đề ${notEmpty}`,
              },
              {
                max: 150,
                message: `Tối đa 150 ký tự`,
              },
            ]}
          >
            <Input placeholder={'Tiêu đề'} maxLength={150} />
          </Form.Item>
          <Form.Item
            label={'Nội dung*'}
            name={'description'}
            rules={[
              {
                required: true,
                message: `Nội dung ${notEmpty}`,
              },
              {
                max: 750,
                message: `Tối đa 750 ký tự`,
              },
            ]}
          >
            <TextArea className={'w-full rounded-sm !bg-[#F2F2F4] p-[6px]'} rows={3} maxLength={750} />
          </Form.Item>

          <div className={'flex justify-end'}>
            <Button
              type={'default'}
              className={'mr-[16px] w-[95px]'}
              onClick={() => {
                resetTarget();
              }}
            >
              Hủy
            </Button>
            <Button htmlType={'submit'} type={'primary'} className={'px-[24px]'}>
              Thêm
            </Button>
          </div>
        </div>
      </Form>
    );
  };
  return (
    <div className={'target-content p-[16px]'}>
      <p className={'mb-[12px] bg-cover bg-center font-bold'}>Mục tiêu học tập</p>
      {courseInfo?.courseThumbnailImage ? (
        <div
          className={'h-[120px] bg-cover bg-center'}
          style={{ backgroundImage: 'url(' + courseInfo?.courseThumbnailImage + ')' }}
        />
      ) : (
        <Image width={790} height={120} alt={'target-image'} className={'w-full'} src={TargetImage} />
      )}

      <div className={'bg-white'}>
        <div className={'flex'}>
          <div className={'flex w-1/2 items-center bg-primary p-[12px]'}>
            <span className={'text-xs text-white'}>
              Bài học sẽ bắt đầu sau <span className={'text-secondary-500'}>{time_auto_switch} giây</span>
            </span>
          </div>
          <div className={'flex w-1/2 items-center justify-end bg-secondary-500 p-[12px]'}>
            <span className={'mr-2 text-xs font-bold'}>{getNextSection}</span>
            <Image src={PlayIcon} alt={'play-icon'} width={24} height={24} />
          </div>
        </div>

        <div className={'mb-[24px] mt-[32px] text-center'}>
          <h6 className={'text-xl font-bold'}>Mục tiêu học tập</h6>
          <p>Trong chương này, bạn sẽ tìm hiểu các nội dung sau</p>
        </div>

        <div className={'target-content__content mx-[32px] min-h-[calc(100vh_-_480px)]'}>
          <div className={'min-h-[calc(100vh_-_510px)] rounded-md bg-[#F2F2F4] p-[12px]'}>
            <TargetCollapse
              targets={targetStore.learning_goal_content || []}
              setTargets={handleSetTargets}
              setCurrentTarget={setCurrentTarget}
            />

            {targetStore.learning_goal_content?.length < 5 && isShowAddButton ? (
              <div
                className={'mt-2 w-full cursor-pointer rounded-sm border border-dashed bg-white p-[12px]'}
                onClick={handleAddTarget}
              >
                <PlusCircleOutlined />
                <span className={'ml-2'}>Thêm thẻ đề mục</span>
              </div>
            ) : null}
            {!isShowAddButton && renderTextEditor()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TargetContent;
