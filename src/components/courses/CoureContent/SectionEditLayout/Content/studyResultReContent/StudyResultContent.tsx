import { CourseInfo, Section, UploadedFile } from '@/features/courses';
import { Col, Collapse, Form, Image, Row, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import AttachDocument from 'components/courses/CoureContent/SectionEditLayout/Content/studyResultReContent/components/AttachDocument';
import { SectionType } from 'config';
import { CompleteRateMode, HasAutoSwitch } from 'constants/enum';
import { useQuerySearch } from 'hooks';
import DownArrowIcon from 'icons/DownArrow';
import PlayResultIcon from 'icons/PlayResultIcon';
import { StudyResultImage } from 'images';
import { isNil } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setCurrentResultForStore,
  setReferDocumentsToStore,
  setStudyMaterialsToStore,
} from 'reducer/courseReducer/targetSlice';
import { RootState } from 'store/store';
import { TargetItem } from 'type/course/target';

import { getIndexSectionInCourse } from 'utils/convert';
import './index.scss';

const defaultTime = 15;
const defaultRatePercent = 15;

type StudyResultContentProps = {
  section: Section | undefined;
  courseInfo: CourseInfo | undefined;
};

const StudyResultContent = ({ section, courseInfo }: StudyResultContentProps) => {
  const dispatch = useDispatch();
  const [form] = useForm();

  const queryData = useQuerySearch();

  const studyResult = useSelector((state: RootState) => state.target.studyResult);
  const { time_auto_switch, complete_rate_value } = studyResult;
  const [targets, setTargets] = useState<TargetItem[]>([]);
  const [referDocuments, setReferDocuments] = useState<string[]>([]);
  const [studyMaterials, setStudyMaterials] = useState<UploadedFile[]>([]);

  useEffect(() => {
    // set data to redux when load page
    if (!courseInfo || !section) return;
    const { learningGoal } = section;
    const studyTarget =
      Array.isArray(courseInfo?.sections) &&
      courseInfo.sections.find((section) => section.sectionTypeId === SectionType.Target);
    if (studyTarget) {
      setTargets(studyTarget?.learningGoal?.learning_goal_content);
    }
    const {
      time_auto_switch,
      has_auto_switch,
      complete_rate_type,
      complete_rate_value,
      study_materials,
      refer_documents,
    } = learningGoal;
    dispatch(
      setCurrentResultForStore({
        ...learningGoal,
        learning_goal_content: studyTarget ? studyTarget.learningGoal.learning_goal_content : [],
        time_auto_switch: time_auto_switch ? time_auto_switch : defaultTime,
        has_auto_switch: isNil(has_auto_switch) ? HasAutoSwitch.ON : has_auto_switch,
        complete_rate_type: complete_rate_type ? complete_rate_type : CompleteRateMode.TU_DONG,
        complete_rate_value: complete_rate_value ? complete_rate_value : defaultRatePercent,
      } as any),
    );
    setStudyMaterials(study_materials || []);
    setReferDocuments(refer_documents || []);
  }, [courseInfo]);

  useEffect(() => {
    dispatch(setStudyMaterialsToStore(studyMaterials));
  }, [studyMaterials]);

  useEffect(() => {
    dispatch(setReferDocumentsToStore(referDocuments));
  }, [referDocuments]);

  const getNextSection = useMemo(() => {
    const currentSection = getIndexSectionInCourse(courseInfo as any, queryData?.sectionId);
    return courseInfo?.sections?.[currentSection + 1]?.sectionName;
  }, [courseInfo, queryData]);

  return (
    <div className={'target-content p-[16px]'}>
      <p className={'mb-[12px] font-bold'}>Kết quả học tập</p>

      <div className={'bg-white px-[50px] pb-[22px] pt-[32px]'}>
        <Row gutter={40}>
          <Col span={12} className={'flex justify-center'}>
            <Image width={298} height={328} alt={'target-image'} src={StudyResultImage} preview={false} />
          </Col>
          <Col span={12}>
            <p className={'mb-[16px] text-6xl font-bold text-primary'}>Chúc mừng!</p>
            <Space className={'mb-[48px] w-full'} size={2} direction={'vertical'}>
              <p>Bạn đã hoàn thành chương {getIndexSectionInCourse(courseInfo as any, queryData?.sectionId)}:</p>
              <p className={'text-xl font-semibold'}>{section?.sectionName}</p>
              <p>Thời lượng khóa học đã hoàn thành: {complete_rate_value}%</p>
            </Space>
            <div className={'flex items-center'}>
              <DownArrowIcon />
              <p className={'ml-[10px] text-[10px]'}>
                Cuộn chuột để <br />
                tìm hiểu thêm
              </p>
            </div>
          </Col>
        </Row>
      </div>

      <div className={'bg-white'}>
        <div className={'flex'}>
          <div className={'flex w-1/2 flex-col justify-center bg-primary p-[16px]'}>
            <p className={'text-xs text-[#E5E5E9]'}>Tự động chuyển tiếp sau {time_auto_switch} giây</p>
            <p className={'text-xs italic text-[#E5E5E9]'}>(Tương tác với nội dung để dừng chế độ tự động)</p>
          </div>
          <div className={'flex w-1/2 items-center justify-end bg-[#cccccc] p-[16px]'}>
            <div className={'mr-3 flex flex-col justify-end'}>
              <p className={'text-xs text-[#E5E5E9]'}>Phần tiếp theo</p>
              <p className={'text-xs font-bold text-[#E5E5E9]'}>{getNextSection}</p>
            </div>
            <PlayResultIcon />
          </div>
        </div>

        <div className={'study-result-content m-[24px] min-h-[calc(100vh_-_480px)]'}>
          <Form form={form} size={'small'}>
            <div className={'rounded-[8px]'}>
              <Row gutter={12}>
                <Col span={12}>
                  <div className={'min-h-[220px] rounded-[8px] bg-[#F2F2F4] p-[12px]'}>
                    <p className={'mb-[6px] font-semibold'}>Nội dung tổng quan</p>
                    {Array.isArray(targets) &&
                      targets.map((target: TargetItem, index: number) => (
                        <Collapse
                          key={index}
                          expandIconPosition="end"
                          size={'small'}
                          className={'bg-white'}
                          bordered={false}
                          defaultActiveKey={0}
                        >
                          <Collapse.Panel
                            key={index}
                            style={{ border: 'none' }}
                            collapsible="icon"
                            header={<span className={'text-xs font-semibold'}>{target.title}</span>}
                            className={'mb-[16px]'}
                          >
                            <p className={'text-xs'}>{target.description}</p>
                          </Collapse.Panel>
                        </Collapse>
                      ))}
                  </div>
                </Col>
                <AttachDocument
                  referDocuments={referDocuments}
                  setReferDocuments={setReferDocuments}
                  studyMaterials={studyMaterials as any}
                  setStudyMaterials={setStudyMaterials as any}
                />
              </Row>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default StudyResultContent;
