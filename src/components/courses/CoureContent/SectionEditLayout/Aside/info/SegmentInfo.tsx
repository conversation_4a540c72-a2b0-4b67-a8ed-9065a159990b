import { FileType } from '@/constants/file';
import { Lecture, mapFileTypeToFileTypeId, Section } from '@/features/courses';
import { Col, Form, Input, Row, TimePicker } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { notEmpty } from 'config/constant';
import { AsideTab } from 'constants/enum';
import dayjs, { Dayjs } from 'dayjs';
import { useQuerySearch } from 'hooks';
import { ImageSegment } from 'images';
import { isEmpty, isNil } from 'lodash-es';
import Image from 'next/image';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setActionType, setFileType } from 'reducer/courseReducer/commonSlide';
import { setSegmentsState, setSelectedSegment } from 'reducer/courseReducer/courseSlice';
import { RootState } from 'store/store';
import { LectureSegment, QueryParams } from 'type';
import {
  convertDayjsToHHMMSS,
  convertHHMMSSToSeconds,
  convertPercentToSecond,
  convertSecondToPercent,
  secondToHHMMSS,
} from 'utils/convert';

type SegmentInfoProps = {
  section: Section | undefined;
};

export const SegmentInfo = ({ section }: SegmentInfoProps) => {
  const [form] = useForm();
  const dispatch = useDispatch();
  const searchUrl: QueryParams = useQuerySearch();
  const selectedSegment = useSelector((state: RootState) => state.course.selectedSegment);
  const segmentsStore = useSelector((state: RootState) => state.course.segments);
  const totalTimeVideo = useSelector((state: RootState) => state.course.totalTimeVideo);

  useEffect(() => {
    if (selectedSegment.id) {
      dispatch(setActionType(AsideTab.Info));
    }

    if (selectedSegment) {
      form.setFieldsValue({
        segment_name: selectedSegment.segment_name,
        start_at: dayjs(secondToHHMMSS(convertPercentToSecond(selectedSegment.start_at, totalTimeVideo)), 'HH:mm:ss'),
        end_at: dayjs(secondToHHMMSS(convertPercentToSecond(selectedSegment.end_at, totalTimeVideo)), 'HH:mm:ss'),
        total_time: dayjs(
          secondToHHMMSS(convertPercentToSecond(selectedSegment.end_at - selectedSegment.start_at, totalTimeVideo)),
          'HH:mm:ss',
        ),
      });
    }

    if (!selectedSegment.id) {
      form.setFields([
        {
          name: 'segment_name',
          errors: [],
        },
      ]);
      return;
    }

    if (isEmpty(form.getFieldValue('segment_name'))) {
      form.setFields([
        {
          name: 'segment_name',
          errors: [`Tên phân đoạn ${notEmpty}`],
        },
      ]);
    }
  }, [selectedSegment]);

  const onChangeSegmentName = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const segment = { ...selectedSegment, segment_name: value };
    dispatch(setSelectedSegment(segment));
    const newSegmentsStore = [...segmentsStore];
    const idx = newSegmentsStore.findIndex((item) => item.id === segment.id);
    if (idx !== -1) {
      newSegmentsStore[idx] = segment;
      dispatch(setSegmentsState(newSegmentsStore));
    }
  };

  const handleInValidTime = () => {
    if (selectedSegment.id) {
      form.setFields([
        {
          name: 'start_at',
          errors: ['Thời gian bắt đầu nhỏ hơn thời gian đến'],
        },
      ]);
      form.setFieldValue('total_time', dayjs('00:00:00', 'HH:mm:ss'));
    }
  };

  const checkInValidTimeSegment = (
    segmentsState: LectureSegment[],
    selectedSegment: { id: number; start_at: number; end_at: number },
  ) => {
    if (selectedSegment.id === 0) return false;
    const idx = segmentsState.findIndex((segment) => segment.id === selectedSegment.id);
    if (idx !== -1) {
      const leftSegment = { ...segmentsState[idx - 1] };
      const rightSegment = { ...segmentsState[idx + 1] };

      if (selectedSegment.start_at <= leftSegment.end_at) {
        return 'start_at';
      } else if (selectedSegment.end_at >= rightSegment.start_at) {
        return 'end_at';
      } else {
        return false;
      }
    }
  };

  const handleUpdateTime = (start: Dayjs, end: Dayjs) => {
    if (!isNil(start) && !isNil(end)) {
      const diff = end.diff(start);

      const isInvalidTime = checkInValidTimeSegment(segmentsStore, {
        id: selectedSegment.id!,
        start_at: convertSecondToPercent(convertHHMMSSToSeconds(start.format('HH:mm:ss')), totalTimeVideo),
        end_at: convertSecondToPercent(convertHHMMSSToSeconds(end.format('HH:mm:ss')), totalTimeVideo),
      });
      if (isInvalidTime) {
        form.setFields([
          {
            name: isInvalidTime,
            errors: ['Thời gian không hợp lệ'],
          },
        ]);
        return;
      }
      if (diff >= 0) {
        handleValidTime(start, end);
      } else {
        handleInValidTime();
      }
    }
  };

  const handleValidTime = (start: Dayjs, end: Dayjs) => {
    const total_time = convertDayjsToHHMMSS(start, end);
    form.setFieldValue('total_time', dayjs(total_time, 'HH:mm:ss'));
    form.setFields([
      {
        name: 'start_at',
        errors: [],
      },
    ]);

    const idx = segmentsStore.findIndex((segment) => segment.id === selectedSegment.id);

    if (idx !== -1) {
      const newSelectSegment = { ...segmentsStore[idx] };
      newSelectSegment['start_at'] = convertSecondToPercent(
        convertHHMMSSToSeconds(start.format('HH:mm:ss')),
        totalTimeVideo,
      );
      newSelectSegment['end_at'] = convertSecondToPercent(
        convertHHMMSSToSeconds(end.format('HH:mm:ss')),
        totalTimeVideo,
      );

      dispatch(setSelectedSegment(newSelectSegment));
      const newSegmentState = [...segmentsStore];
      newSegmentState[idx] = newSelectSegment;
      dispatch(setSegmentsState(newSegmentState));
    }
  };

  return (
    <div className={'p-[16px]'}>
      <div className={'mb-[12px] flex justify-between'}>
        <p className={'font-semibold'}>Nội dung</p>
        <p
          onClick={() => {
            dispatch(setActionType(AsideTab.Library));
            dispatch(setFileType(mapFileTypeToFileTypeId(FileType.VIDEO)));
          }}
          className={'cursor-pointer text-primary'}
        >
          Đổi video khác
        </p>
      </div>

      <div className={'relative'}>
        <div
          className={
            'wrap-image height-full mb-[35px] flex h-[144px] w-full items-center justify-center rounded-md bg-primary'
          }
        >
          <Image width={52} height={32} src={ImageSegment} alt={'segment-image'} />
        </div>
        <p className={'absolute bottom-[16px] left-[20px] text-sm text-white'}>
          {section?.lectures.find((lecture: Lecture) => lecture.id === searchUrl?.lectureId)?.video?.fileName}
        </p>
      </div>

      <Form form={form} layout={'vertical'}>
        <Row gutter={[12, 0]}>
          <Col span={24}>
            <Form.Item
              label={'Tên phân đoạn*'}
              name={'segment_name'}
              required={true}
              rules={[
                {
                  required: true,
                  message: `Tên phân đoạn ${notEmpty}`,
                },
              ]}
            >
              <Input placeholder={'Tên phân đoạn'} onChange={onChangeSegmentName} disabled={!selectedSegment.id} />
            </Form.Item>
          </Col>

          <Col span={24}>
            <p className={'pb-[16px] font-bold'}>Thời lượng</p>
          </Col>

          {/*<Col span={12}>*/}
          {/*  <Form.Item label={'Bắt đầu từ*'} name={'start_at'}>*/}
          {/*    <TimePicker*/}
          {/*      showNow={false}*/}
          {/*      placeholder={'HH:mm:ss'}*/}
          {/*      disabled={!selectedSegment.id}*/}
          {/*      onChange={(start: Dayjs | null) => {*/}
          {/*        if (start) {*/}
          {/*          handleUpdateTime(start, form.getFieldValue('end_at'));*/}
          {/*        }*/}
          {/*      }}*/}
          {/*    />*/}
          {/*  </Form.Item>*/}
          {/*</Col>*/}

          {/*<Col span={12}>*/}
          {/*  <Form.Item label={'Đến*'} name={'end_at'}>*/}
          {/*    <TimePicker*/}
          {/*      placeholder={'hh:mm:ss'}*/}
          {/*      disabled={!selectedSegment.id}*/}
          {/*      showNow={false}*/}
          {/*      onChange={(end: Dayjs | null) => {*/}
          {/*        if (end) {*/}
          {/*          handleUpdateTime(form.getFieldValue('start_at'), end);*/}
          {/*        }*/}
          {/*      }}*/}
          {/*    />*/}
          {/*  </Form.Item>*/}
          {/*</Col>*/}

          <Col span={24}>
            <Form.Item label={'Thời lượng phát*'} name={'total_time'}>
              <TimePicker disabled placeholder={'hh:mm:ss'} className={'w-full'} showNow={false} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
