import { revalidatePath } from '@/actions/revalidatePath';
import { CreateQuestionRequest, SlideItemRequest, useCourseCreationMutation } from '@/features/courses';
import {
  CASE_STUDY_INITIAL_VALUE,
  lectureInteractionInitialValue,
} from '@/features/courses/creation/constants/lecture';
import {
  caseStudyInitial,
  exploreInitial,
  questionInitial,
  slideInitial,
} from '@/features/courses/creation/constants/slide';
import { LectureInteract, SlideItem } from '@/features/courses/types';
import { secondsToTimestamp } from '@/lib/dateTime';
import { Button, Form, Image, Modal, Space, TimePicker, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { notification } from 'antd/lib';
import { FileType, LectureType } from 'config/constant';
import { AsideTab } from 'constants/enum';
import { InteractionType, MM_SS } from 'constants/index';
import { useQuerySearch } from 'hooks';
import { ImageAudio, ImageContent, ImageDiscover, ImageShortClip, ImageSituation } from 'images';
import { omit } from 'lodash-es';
import { useParams, usePathname } from 'next/navigation';
import { useContext, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setActionType, setFileType } from 'reducer/courseReducer/commonSlide';
import { initCaseStudy, initSlideItem } from 'reducer/courseReducer/initValueState';
import { setSlides } from 'reducer/courseReducer/slideSlice';
import { RootState } from 'store/store';
import {
  Validator,
  dateToTimeStamp,
  getInteractionName,
  handleCheckInteractionStartTime,
  notifyWarning,
  timeStampToSecond,
} from 'utils';
import UserInfoContext from 'utils/providers/UserInfoProvider';
import './index.scss';

type InteractionProps = {
  isLectureTypeSlide: boolean;
};

const Interaction = (props: InteractionProps) => {
  const { isLectureTypeSlide } = props;
  const [form] = useForm();

  const { onAddSlideItem, onAddVideoQuestion } = useCourseCreationMutation();

  const pathname = usePathname();
  const params: { courseId: string } = useParams();

  const queryParams = useQuerySearch();
  const dispatch = useDispatch();
  const slides = useSelector((state: RootState) => state.slides.slides);
  const selectedSlideItem = useSelector((state: RootState) => state.slides.selectedSlideItem);
  const selectedLecture = useSelector((state: RootState) => state.course.selectedLecture);

  const totalTimeVideo = useSelector((state: RootState) => state.course.totalTimeVideo);
  const [isShowPopConfirm, setIsShowPopConfirm] = useState<boolean>(false);
  const [interactionType, setInteractionType] = useState<number>(-1);

  const lectureInteracts = selectedLecture?.lectureInteracts || [];

  const [startTime, setStartTime] = useState<number | null>(null);
  const { isVipUser } = useContext(UserInfoContext);

  const isVideoLecture: boolean = useMemo(
    () => selectedLecture?.lectureTypeId === LectureType.Video,
    [selectedLecture],
  );

  const isDisableInteraction = useMemo(
    () => selectedLecture.lectureTypeId === LectureType.Video && totalTimeVideo === 0,
    [totalTimeVideo],
  );

  const handleCreateVideoInteraction = (startTime: number, lecture_id: string) => {
    const durationTimeStamp = secondsToTimestamp(selectedLecture.video?.fileDuration || 0);

    const questionPayload = {
      ...questionInitial,
      video_url: selectedLecture?.video?.fileUrl,
    };

    const initValue = {
      ...lectureInteractionInitialValue,
      start_at: startTime,
      interact_type_id: interactionType,
      question: interactionType === InteractionType.Question ? questionPayload : null,
      explore: interactionType === InteractionType.Explore ? exploreInitial : null,
      case_study: interactionType === InteractionType.Situation ? initCaseStudy : null,
      lecture_id,
      interact_name: getInteractionName(interactionType),
      duration: durationTimeStamp,
    } satisfies LectureInteract;

    const quickQuestionsPayload = [...lectureInteracts, initValue] as LectureInteract[];

    const requestPayload = {
      courseId: params?.courseId || '',
      sectionId: queryParams?.sectionId || '',
      lectureId: queryParams?.lectureId || '',
      payload: { lecture_interacts: quickQuestionsPayload },
    } satisfies CreateQuestionRequest;

    onAddVideoQuestion(requestPayload, {
      onSuccess: async () => {
        notification.success({ message: 'Thêm tương tác cho video thành công' });
        setIsShowPopConfirm(false);
        setInteractionType(-1);
        await revalidatePath(pathname, 'page');
      },
    });
  };

  const handleConfirmAddQuestion = () => {
    if (!startTime) return;

    handleCreateVideoInteraction(startTime, queryParams?.lectureId || '');
  };

  const handleCancelAddQuestion = () => {
    setIsShowPopConfirm(false);
    setInteractionType(-1);
    setStartTime(null);
    form.resetFields(['start_time']);
  };

  const preventCreateInteract = () => {
    if (!totalTimeVideo) {
      message.warning('Vui lòng thêm video cho bài giảng trước!');
      return;
    }
  };

  const handleAddQuickQuestionForVideo = () => {
    preventCreateInteract();
    setIsShowPopConfirm(true);
    setInteractionType(InteractionType.Question);
  };
  const handleAddSituationForVideo = () => {
    preventCreateInteract();
    setIsShowPopConfirm(true);
    setInteractionType(InteractionType.Situation);
  };
  const handleAddExploreForVideo = () => {
    preventCreateInteract();
    setIsShowPopConfirm(true);
    setInteractionType(InteractionType.Explore);
  };

  const handleAddInteraction = (initValue: SlideItem) => {
    const idx = slides.findIndex((slide) => slide.id === selectedSlideItem.id);

    const newSlides = [...slides];
    if (idx !== -1 && selectedLecture?.slide) {
      newSlides.splice(idx, 0, initValue);
    } else {
      newSlides.push(initValue);
    }
    dispatch(setSlides(newSlides));
  };

  const handleAddQuickQuestionForSlide = () => {
    if (!selectedLecture?.slide) return;

    const initValue = {
      ...slideInitial,
      slide_item_name: 'Câu hỏi nhanh',
      slide_item_type_id: InteractionType.Question,
      question: omit(questionInitial, 'id'),
      case_study: CASE_STUDY_INITIAL_VALUE,
      sort_index: slides.length + 1,
    } satisfies SlideItemRequest;

    const existingSlides = slides.map((slide) => ({
      ...slide,
      sound: slide.sound?.id || null,
    }));

    const slidePayload = [...existingSlides, initValue] as SlideItemRequest[];

    onAddSlideItem(
      {
        courseId: params?.courseId || '',
        sectionId: queryParams?.sectionId || '',
        lectureId: queryParams?.lectureId || '',
        slideId: selectedLecture.slide.id,
        payload: { data: slidePayload },
      },
      {
        onSuccess: async () => {
          handleAddInteraction(initValue as SlideItem);
          notification.success({ message: 'Thêm câu hỏi nhanh thành công' });
          await revalidatePath(pathname, 'page');
        },
      },
    );
  };

  const handleAddExploreForSlide = () => {
    if (!selectedLecture?.slide) return;

    const exploreValue = {
      ...slideInitial,
      slide_item_name: 'Khám phá',
      slide_item_type_id: InteractionType.Explore,
      explore: exploreInitial,
      sort_index: slides.length + 1,
    };

    const existingSlides = slides.map((slide) => ({
      ...slide,
      sound: slide.sound?.id || null,
    }));

    const slidePayload = [...existingSlides, exploreValue];

    onAddSlideItem(
      {
        courseId: params?.courseId || '',
        sectionId: queryParams?.sectionId || '',
        lectureId: queryParams?.lectureId || '',
        slideId: selectedLecture.slide.id,
        payload: { data: slidePayload },
      },
      {
        onSuccess: async () => {
          handleAddInteraction(exploreValue as SlideItem);
          notification.success({ message: 'Thêm khám phá thành công' });
          await revalidatePath(pathname, 'page');
        },
      },
    );
  };

  const handleAddSituationForSlide = () => {
    if (!selectedLecture?.slide) return;

    const initValue: SlideItem = {
      ...initSlideItem,
      slide_item_type_id: InteractionType.Situation,
      case_study: caseStudyInitial,
      background_image: '',
    };

    handleAddInteraction(initValue);
  };

  const handleAddShortVideo = () => {
    dispatch(setFileType(FileType.Video));
    dispatch(setActionType(AsideTab.Library));
  };

  const handleAddAudio = () => {
    dispatch(setFileType(FileType.Audio));
    dispatch(setActionType(AsideTab.Library));
  };

  const handleChangeTime = (time: any) => {
    const timestamp = dateToTimeStamp(time);
    if (!timestamp) return;
    setStartTime(timestamp);
  };

  const onFinish = () => {
    form
      .validateFields()
      .then(() => {
        handleConfirmAddQuestion();
      })
      .catch();
  };

  const onAddInteraction = (fn1: () => void, fn2: () => void) => {
    const isValid = Validator.isValidFreeInteraction(isVipUser, selectedLecture);
    if (isValid) {
      const executeFunction = isVideoLecture ? fn1 : fn2;
      executeFunction();
    } else {
      notifyWarning(
        'Tài khoản miễn phí chỉ được tạo tối đa 1 tương tác trên 1 bài học. Nâng cấp để không giới hạn tạo tương tác.',
      );
    }
  };

  return (
    <div className={'interaction relative h-full w-[288px]'}>
      <div className={'border-b-1 w-[full] border-[#e5e5e9] px-[16px] py-[14px]'}>
        <p className={'font-semibold'}>Tương tác</p>
      </div>
      <div className={'p-[16px]'}>
        <Space direction={'vertical'} size={4}>
          <Space size={28}>
            <div
              className={`cursor-pointer ${totalTimeVideo === 0 && isVideoLecture ? 'opacity-30' : ''}`}
              onClick={() => onAddInteraction(handleAddQuickQuestionForVideo, handleAddQuickQuestionForSlide)}
            >
              <Image src={ImageContent} alt={'ImageContent'} preview={false} />
              <p className={'text-center text-sm'}>Câu hỏi nhanh</p>
            </div>

            <div
              className={`cursor-pointer ${totalTimeVideo === 0 && isVideoLecture ? 'opacity-30' : ''}`}
              onClick={() => onAddInteraction(handleAddSituationForVideo, handleAddSituationForSlide)}
            >
              <Image src={ImageSituation} alt={'ImageSituation'} preview={false} />
              <p className={'text-center text-sm'}>Tình huống</p>
            </div>
          </Space>

          <Space size={28}>
            <div
              className={`cursor-pointer ${totalTimeVideo === 0 && isVideoLecture ? 'opacity-30' : ''}`}
              onClick={() => onAddInteraction(handleAddExploreForVideo, handleAddExploreForSlide)}
            >
              <Image src={ImageDiscover} alt={'ImageDiscover'} preview={false} />
              <p className={'text-center text-sm'}>Khám phá</p>
            </div>
            {isLectureTypeSlide && (
              <div className={'cursor-pointer'} onClick={handleAddShortVideo}>
                <Image src={ImageShortClip} alt={'ImageShortClip'} preview={false} />
                <p className={'text-center text-sm'}>Clip ngắn</p>
              </div>
            )}
          </Space>
          {isLectureTypeSlide && (
            <Space size={28}>
              <div className={'cursor-pointer'} onClick={handleAddAudio}>
                <Image src={ImageAudio} alt={'ImageAudio'} preview={false} />
                <p className={'text-center text-sm'}>Âm thanh</p>
              </div>
            </Space>
          )}
        </Space>
      </div>
      <Modal
        title={'Thời gian bắt đầu tương tác'}
        onCancel={handleCancelAddQuestion}
        className={'popup-select-time'}
        open={isShowPopConfirm}
        width={320}
        footer={[
          <Button key={'cancel'} onClick={handleCancelAddQuestion} className={'w-[110px]'}>
            Huỷ
          </Button>,
          <Button type="primary" key={'ok'} onClick={onFinish} className={'w-[110px] !bg-orange-600'}>
            Đồng ý
          </Button>,
        ]}
      >
        <Form form={form}>
          <Form.Item
            name={'start_time'}
            rules={[
              { required: true, message: 'Vui lòng chọn thời gian bắt đầu' },
              {
                validator: (_, date: any) => {
                  if (!date) return Promise.resolve();
                  const startTime = timeStampToSecond(dateToTimeStamp(date));
                  return handleCheckInteractionStartTime(startTime, totalTimeVideo);
                },
              },
            ]}
          >
            <TimePicker showNow={false} placeholder={MM_SS} format={MM_SS} onChange={handleChangeTime} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Interaction;
