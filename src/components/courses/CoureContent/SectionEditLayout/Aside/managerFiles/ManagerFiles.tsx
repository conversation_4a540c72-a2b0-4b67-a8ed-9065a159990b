import { FileActions, FileType } from '@/constants/file';
import { UploadedFile } from '@/features/courses';
import { CaretRightOutlined } from '@ant-design/icons';
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  MoreOutlined,
  PauseOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons/lib/icons';
import { Form, Input, Popover, Space, Tooltip } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { LectureType } from 'config/constant';
import { ReactNode, useState } from 'react';
import H5AudioPlayer from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';
import ReactPlayer from 'react-player/lazy';
import { useSelector } from 'react-redux';
import { RootState } from 'store/store';

import Image from 'next/image';

const actionWithFile: {
  icon: ReactNode | null;
  label: ReactNode;
  value: FileActions;
}[] = [
  {
    icon: <PlusCircleOutlined />,
    label: 'Thêm vào bài giảng',
    value: FileActions.Add,
  },
  {
    icon: <DownloadOutlined />,
    label: 'Tả<PERSON> về',
    value: FileActions.Download,
  },
  {
    icon: <DeleteOutlined />,
    label: 'Xóa tệp',
    value: FileActions.Delete,
  },
];

type ManagerFilesProps = {
  fileType: FileType;
  handleUpdateFileName: (value: string, data: UploadedFile) => void;
  handleActionWithFile: (value: FileActions, file: UploadedFile) => void;
  files: UploadedFile[];
};

const ManagerFiles = ({ fileType, handleUpdateFileName, handleActionWithFile, files }: ManagerFilesProps) => {
  const [form] = useForm();
  const [fileIdHover, setFileIdHover] = useState<number>(0);
  const selectedLecture = useSelector((state: RootState) => state.course.selectedLecture);

  const [playingVideos, setPlayingVideos] = useState<string[]>([]);

  const [isEditTitle, setIsEditTitle] = useState<boolean>(false);

  const getListFiles = (courseFiles: UploadedFile[]) => {
    return (
      <Space
        className={'h-[calc(100vh_-_160px)] w-full overflow-x-hidden overflow-y-scroll p-[16px]'}
        size={16}
        direction={'vertical'}
      >
        {courseFiles.map((courseFile, idx) => {
          switch (courseFile.fileType) {
            case FileType.VIDEO.toUpperCase():
              return childrenContent(FileType.VIDEO, courseFile, idx);
            case FileType.IMAGE.toUpperCase():
              return childrenContent(FileType.IMAGE, courseFile, idx);
            case FileType.SLIDE.toUpperCase():
              return childrenContent(FileType.SLIDE, courseFile, idx);
            case FileType.AUDIO.toUpperCase():
              return childrenContent(FileType.AUDIO, courseFile, idx);
          }
        })}
      </Space>
    );
  };
  const infoElement =
    'rounded-[6px] w-[24px] h-[24px] bg-[#999999] absolute top-4 right-3 flex justify-center cursor-pointer';

  const childrenContent = (fileType: FileType, data: UploadedFile, idx: number) => {
    return (
      <div
        className={`${
          idx === fileIdHover ? 'border-secondary-500' : 'border-ink-200'
        } wrap-video relative rounded-sm border bg-[#F2F2F4] ${
          fileType === FileType.AUDIO ? '!h-[100px]' : 'h-[140px]'
        }`}
        onMouseOver={() => {
          setFileIdHover(idx);
        }}
        onMouseOut={() => {
          setFileIdHover(-1);
        }}
        key={`${fileType}-${data.id}`}
        onDoubleClick={() => {
          handleActionWithFile(FileActions.AddAudioForSlide, data);
        }}
      >
        {fileType === FileType.VIDEO && (
          <ReactPlayer url={data.fileUrl} width="100%" height="100%" playing={false} muted={true} />
        )}
        {fileType === FileType.IMAGE && (
          <Image
            src={data.fileUrl}
            alt={data.fileUrl}
            width={288}
            height={140}
            className="size-full rounded-sm bg-cover bg-center"
          />
        )}
        {fileType === FileType.AUDIO && (
          <div className="flex size-full justify-center">
            <span className={'w-full cursor-pointer'}>
              <H5AudioPlayer
                className={'manage-file'}
                src={data.fileUrl}
                autoPlay={false}
                showJumpControls={false}
                showDownloadProgress={false}
                // other props here
                showFilledVolume={false}
                showFilledProgress={false}
                showSkipControls={false}
                customVolumeControls={[]}
                customAdditionalControls={[]}
                layout="horizontal-reverse"
                onPause={() => {
                  setPlayingVideos((prev) => prev.filter((item) => item !== data.id));
                }}
                onPlaying={() => {
                  if (!playingVideos.includes(data.id)) {
                    setPlayingVideos((prev) => [...prev, data.id]);
                  }
                }}
                customIcons={{
                  play: (
                    <Tooltip title={'Nghe thử'}>
                      <div className={'flex size-8 items-center justify-center rounded-full bg-[#D1D2FE]'}>
                        <CaretRightOutlined size={16} className={'size-5 text-black'} />
                      </div>
                    </Tooltip>
                  ),
                  pause: (
                    <Tooltip title={'Dừng'}>
                      <div className={'flex size-8 items-center justify-center rounded-full bg-[#D1D2FE]'}>
                        <PauseOutlined size={16} className={'size-5 text-black'} />
                      </div>
                    </Tooltip>
                  ),
                }}
                customProgressBarSection={[
                  <div key={'section'}>
                    {playingVideos.includes(data.id) ? (
                      <div className={'loader pr-[42px]'}>
                        {Array.from(Array(40)).map((item) => (
                          <div className={'stroke'} key={item} />
                        ))}
                      </div>
                    ) : (
                      <div key={'section'} className={'pr-[42px]'}>
                        <svg
                          width="160"
                          height="24"
                          viewBox="0 0 182 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="3" y="2" width="2" height="20" fill="#B1B3BF" />
                          <rect x="6" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="9" width="2" height="24" fill="#B1B3BF" />
                          <rect x="12" y="9" width="2" height="6" fill="#B1B3BF" />
                          <rect x="15" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="18" y="4.5" width="2" height="15" fill="#B1B3BF" />
                          <rect x="21" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="24" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="27" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="30" y="2" width="2" height="20" fill="#B1B3BF" />
                          <rect x="33" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="36" width="2" height="24" fill="#B1B3BF" />
                          <rect x="39" y="9" width="2" height="6" fill="#B1B3BF" />
                          <rect x="42" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="45" y="4.5" width="2" height="15" fill="#B1B3BF" />
                          <rect x="48" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="51" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="54" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="57" y="2" width="2" height="20" fill="#B1B3BF" />
                          <rect x="60" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="63" width="2" height="24" fill="#B1B3BF" />
                          <rect x="66" y="9" width="2" height="6" fill="#B1B3BF" />
                          <rect x="69" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="72" y="4.5" width="2" height="15" fill="#B1B3BF" />
                          <rect x="75" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="78" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="81" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="84" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="87" y="2" width="2" height="20" fill="#B1B3BF" />
                          <rect x="90" y="2" width="2" height="20" fill="#B1B3BF" />
                          <rect x="93" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="96" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="99" width="2" height="24" fill="#B1B3BF" />
                          <rect x="102" width="2" height="24" fill="#B1B3BF" />
                          <rect x="105" y="9" width="2" height="6" fill="#B1B3BF" />
                          <rect x="108" y="9" width="2" height="6" fill="#B1B3BF" />
                          <rect x="111" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="114" y="10.5" width="2" height="3" fill="#B1B3BF" />
                          <rect x="117" y="4.5" width="2" height="15" fill="#B1B3BF" />
                          <rect x="120" y="4.5" width="2" height="15" fill="#B1B3BF" />
                          <rect x="123" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="126" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="129" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="132" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="135" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="138" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="141" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="144" y="1" width="2" height="22" fill="#B1B3BF" />
                          <rect x="147" y="2" width="2" height="20" fill="#B1B3BF" />
                          <rect x="150" y="3" width="2" height="18" fill="#B1B3BF" />
                          <rect x="153" y="4" width="2" height="16" fill="#B1B3BF" />
                          <rect x="156" y="5" width="2" height="14" fill="#B1B3BF" />
                          <rect x="159" y="6" width="2" height="12" fill="#B1B3BF" />
                          <rect x="162" y="7" width="2" height="10" fill="#B1B3BF" />
                          <rect x="165" y="8.5" width="2" height="7" fill="#B1B3BF" />
                          <rect x="168" y="8.5" width="2" height="7" fill="#B1B3BF" />
                          <rect x="171" y="10" width="2" height="4" fill="#B1B3BF" />
                          <rect x="174" y="11" width="2" height="2" fill="#B1B3BF" />
                          <rect x="177" y="4.5" width="2" height="15" fill="#B1B3BF" />
                          <rect x="180" y="4.5" width="2" height="15" fill="#B1B3BF" />
                        </svg>
                      </div>
                    )}
                  </div>,
                ]}
              />
              {/*<img src={SoundPreview} className={'mt-[20px]'} />*/}
            </span>
          </div>
        )}
        <p
          className={`${
            fileType === FileType.AUDIO ? '!text-black' : 'text-white'
          } absolute bottom-[12px] left-[12px] w-[calc(100%-24px)] break-words text-xs`}
        >
          {data.fileName}
        </p>
        {[FileType.VIDEO, FileType.IMAGE, FileType.AUDIO].includes(fileType) && (
          <Popover
            content={() => popoverContent(data)}
            onOpenChange={() => {
              setIsEditTitle(false);
            }}
            placement={'top'}
            trigger={['click']}
          >
            <div className={'text-primary'}>
              <span className={infoElement}>
                <MoreOutlined />
              </span>
            </div>
          </Popover>
        )}
      </div>
    );
  };

  const canAddVideo = (data: UploadedFile) => {
    return selectedLecture?.lectureTypeId === LectureType.Video && data.fileType === FileType.VIDEO.toUpperCase();
  };

  const popoverContent = (courseFile: UploadedFile) => {
    let newAction = [...actionWithFile];

    if (fileType === FileType.AUDIO && selectedLecture?.lectureTypeId === LectureType.Slide) {
      newAction.push({
        icon: <PlusCircleOutlined />,
        label: 'Thêm âm thanh cho slide',
        value: FileActions.AddAudioForSlide,
      });
    }

    if (fileType === FileType.VIDEO && selectedLecture?.lectureTypeId === LectureType.Slide) {
      newAction.push({
        icon: <PlusCircleOutlined />,
        label: 'Thêm clip ngắn',
        value: FileActions.AddShortClip,
      });
    }
    if (!canAddVideo(courseFile)) {
      newAction = newAction.filter((action) => action.value !== FileActions.Add);
    }
    return (
      <div className={`rounded-md ${fileType === FileType.AUDIO ? '!w-auto py-6' : 'w-[260px]'}`}>
        <ul>
          <div className={'w-[265px] px-[12px]'}>
            {isEditTitle ? (
              <Space size={6} className={'edit-title w-full'}>
                <div>
                  <Form form={form}>
                    <Form.Item name={'title'}>
                      <Input maxLength={150} size={'small'} />
                    </Form.Item>
                  </Form>
                </div>
                <Space size={8} className={'right-0 z-10'}>
                  <CheckOutlined
                    onClick={() => {
                      const title = form.getFieldValue('title');
                      handleUpdateFileName(title, courseFile);
                      setIsEditTitle(false);
                    }}
                  />
                  <CloseOutlined
                    onClick={() => {
                      setIsEditTitle(false);
                    }}
                  />
                </Space>
              </Space>
            ) : (
              <div className={'flex py-[12px]'}>
                <p className={'w-[90%] truncate'}>{courseFile.fileName}</p>
                <div
                  className={'ml-4 w-[10%] cursor-pointer'}
                  onClick={() => {
                    form.setFieldValue('title', courseFile.fileName);
                    setIsEditTitle(true);
                  }}
                >
                  <EditOutlined />
                </div>
              </div>
            )}
          </div>

          {newAction.map((action) => (
            <li
              className={`font-medium h-[56px]${
                action.icon ? 'flex pl-[18px] hover:bg-neutral-50 hover:text-primary' : ''
              } flex cursor-pointer items-center leading-[56px]`}
              key={action.value}
              onClick={() => {
                handleActionWithFile(action.value, courseFile);
              }}
            >
              {action.icon && <span className={'flex hover:text-primary'}>{action.icon}</span>}
              <span className={`inline-block${action.icon ? 'ml-[16px]' : ''}`}>{action.label}</span>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return <div>{getListFiles(files)}</div>;
};

export default ManagerFiles;
