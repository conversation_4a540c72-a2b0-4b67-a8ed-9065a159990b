import { CourseDetailInfo, getLectureName, LectureDetail, Section } from '@/features/courses';
import { <PERSON>ton, Card, Collapse, Space } from 'antd';
import { routePaths } from 'config';
import { IsSequential } from 'constants/enum';
import StarBorderOutlined from 'icons/StarBorderOutlined';
import { ImageCourse } from 'images';
import Image from 'next/image';
import Link from 'next/link';
import queryString from 'query-string';
import { getPreviousLecture } from 'utils/homepage/function';

interface SectionItemProps {
  section: Section;
  activeIds: string[];
  courseInfo: CourseDetailInfo;
}

interface LectureItemProps {
  lecture: LectureDetail;
  idx: number;
  courseInfo: CourseDetailInfo;
  section: Section;
}

const LectureItem = ({ lecture, idx, courseInfo, section }: LectureItemProps) => {
  const previousLecture = getPreviousLecture(courseInfo, lecture);

  const { userCourse } = courseInfo;

  const isRegisteredCourse = userCourse !== null;

  const ViewLecture = () => (
    <div className="text-xs font-semibold text-primary">
      <Link
        href={queryString.stringifyUrl({
          url: routePaths.learner.children.preview.path.replace(':courseId', courseInfo.id?.toString() as string),
          query: {
            lectureId: lecture?.id,
            sectionId: section?.id,
          },
        })}
      >
        <Button type={'link'} className="flex h-auto items-center gap-1 p-0 text-sm">
          Xem thử
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-4">
            <path
              fillRule="evenodd"
              d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm14.024-.983a1.125 1.125 0 010 1.966l-5.603 3.113A1.125 1.125 0 019 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113z"
              clipRule="evenodd"
            />
          </svg>
        </Button>
      </Link>
    </div>
  );

  const CheckSequential = () => (
    <div className="flex items-center gap-1 text-xs font-medium text-ink-700">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-4"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
        />
      </svg>

      {`Mở khóa khi hoàn thành bài ${previousLecture?.lectureName}`}
    </div>
  );

  const getActionForLecture = () => {
    if (isRegisteredCourse) return <></>;
    if (idx !== 0 && courseInfo.isSequential === IsSequential.YES) {
      return <CheckSequential />;
    }
    return <ViewLecture />;
  };

  return (
    <Card>
      <div className="flex gap-4 p-6">
        <div className={'hidden sm:block'}>
          <Image
            alt={lecture.lectureName}
            width={128}
            height={72}
            src={lecture.lectureThumbnailImage ?? ImageCourse}
            className="rounded-md object-cover lg:h-[72px] lg:w-[128px]"
          />
        </div>
        <div className="flex flex-col gap-2">
          <div className="line-clamp-1 text-sm font-semibold">{lecture.lectureName}</div>
          <div className="flex items-center gap-1 text-xs text-ink-700">
            <div>{getLectureName(lecture)}</div>
            {lecture.video?.fileDuration && (
              <>
                <StarBorderOutlined />
                <div>{(parseInt((lecture.video?.fileDuration ?? 0).toString()) / 60).toFixed()} phút</div>
              </>
            )}
          </div>
          {getActionForLecture()}
        </div>
      </div>
    </Card>
  );
};
const SectionItem = ({ section, activeIds, courseInfo }: SectionItemProps) => {
  return (
    <Collapse
      bordered={false}
      defaultActiveKey={activeIds}
      expandIconPosition={'end'}
      items={[
        {
          className: 'bg-ink-50',
          key: section?.id || '',
          label: <div className={'font-semibold'}>{section.sectionName} </div>,
          children: (
            <Space className={'w-full'} direction={'vertical'}>
              {section.lectures.map((lecture, idx) => (
                <LectureItem
                  lecture={lecture as LectureDetail}
                  idx={idx}
                  key={lecture.id}
                  courseInfo={courseInfo}
                  section={section}
                />
              ))}
            </Space>
          ),
        },
      ]}
    />
  );
};

export default SectionItem;
