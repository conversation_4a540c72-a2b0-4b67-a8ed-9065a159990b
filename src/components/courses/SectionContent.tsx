'use client';
import {
  CourseDetailInfo,
  CreateSectionTestRequest,
  LectureType,
  Section,
  SlideItemRequest,
  useCourseCreationMutation,
  useSectionTestMutation,
} from '@/features/courses';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { RootState } from '@/store/store';
import { Button } from 'antd';
import { notification } from 'antd/lib';
import { SectionType } from 'config/constant';
import { useSectionContent } from 'hooks/useSectionContent';
import { omit } from 'lodash-es';
import { ReactNode, useContext } from 'react';
import { useSelector } from 'react-redux';
import { match } from 'ts-pattern';
import UserInfoContext from 'utils/providers/UserInfoProvider';

export type SectionContentProps = {
  courseInfo: CourseDetailInfo;
  section?: Section | undefined;
  isEdit?: boolean;
  children: ReactNode;
};

export const convertBase64ToFormData = (base64String: string): FormData | string => {
  if (!base64String) return '';

  const formData = new FormData();
  formData.append('file', base64String);

  return formData;
};

const SectionContent = (props: SectionContentProps) => {
  const { courseInfo, children, section } = props;
  const { isVipUser } = useContext(UserInfoContext);
  const { isLoadingRelease, queryData, onPublishCourse } = useSectionContent({ ...props });

  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string }>();

  const {
    isAddingQuestion: isUpdatingQuestion,
    isAddingSlide: isUpdatingSlide,
    onAddSlideItem: onSaveSlideEdit,
    onAddVideoQuestion: onSaveVideoEdit,
  } = useCourseCreationMutation();

  const { onCreateQuestionTest: onSaveTestEdit, isCreatingQuestionTest } = useSectionTestMutation();

  const selectedLecture = useSelector((state: RootState) => state.course.selectedLecture);
  const slides = useSelector((state: RootState) => state.slides.slides);

  const currentTest = useSelector((state: RootState) => state.test.currentTest);

  const loading = isUpdatingQuestion || isUpdatingSlide || isCreatingQuestionTest;

  const handleSaveLectureVideo = () => {
    const lectureInteracts = selectedLecture.lectureInteracts || [];

    const payload = {
      courseId: courseInfo?.id?.toString() || '',
      sectionId: queryData?.sectionId || '',
      lectureId: queryData?.lectureId || '',
      payload: { lecture_interacts: lectureInteracts },
    };

    onSaveVideoEdit(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu khóa học thành công' });
      },
    });
  };

  const handleSaveSlides = () => {
    const mappedSlides = slides.map((slide) => {
      return {
        ...slide,
        question: slide.question || null,
        explore: slide.explore || null,
        sound: slide.sound?.id || null,
      };
    }) as SlideItemRequest[];

    const payload = {
      courseId: courseInfo?.id?.toString() || '',
      sectionId: queryData?.sectionId || '',
      lectureId: queryData?.lectureId || '',
      slideId: selectedLecture.slide?.id || '',
      payload: { data: mappedSlides },
    };

    onSaveSlideEdit(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu khóa học thành công' });
      },
    });
  };

  const handleSaveSectionTest = () => {
    const payload = {
      courseId: courseInfo?.id?.toString() || '',
      sectionId: parsedQueryParams?.sectionId || '',
      payload: { ...omit(currentTest, 'id'), updated_at: new Date().toISOString(), test_id: currentTest.id },
    } as CreateSectionTestRequest;

    onSaveTestEdit(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu bài kiểm tra thành công' });
      },
    });
  };

  const handleSaveLectureDetail = () => {
    if (selectedLecture?.id) {
      return match(selectedLecture.lectureTypeId)
        .with(LectureType.Video, handleSaveLectureVideo)
        .with(LectureType.Slide, handleSaveSlides)
        .with(LectureType.Test, handleSaveSectionTest)
        .otherwise(() => {});
    }

    handleSaveSectionTest();
  };

  return (
    <div className={'relative flex h-full justify-center'}>
      <div className={'fixed right-8 top-[10px] z-20 flex justify-end'}>
        <div className={'flex gap-3'}>
          <Button
            className={'w-[98px] bg-white'}
            loading={loading}
            onClick={handleSaveLectureDetail}
            type={'primary'}
            ghost
            disabled={!queryData?.sectionId}
          >
            Lưu
          </Button>

          {isVipUser && (
            <Button
              type={'primary'}
              disabled={!courseInfo.sections?.find((section) => section.sectionTypeId === SectionType.Default)}
              loading={isLoadingRelease}
              // onClick={validateBeforePublish} // TODO: enable this after integrate API add lecture content
              onClick={onPublishCourse}
            >
              Xuất bản
            </Button>
          )}
        </div>
      </div>

      <div className={'content flex h-full w-full'}>{children}</div>
    </div>
  );
};

export default SectionContent;
