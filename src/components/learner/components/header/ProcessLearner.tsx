'use client';

import { CourseInfo } from '@/features/courses';
import DownOutlined from '@ant-design/icons/lib/icons/DownOutlined';
import { Button, Dropdown, MenuProps, Progress } from 'antd';
import { usePermissions } from 'hooks/common';
import Goal from 'icons/Goal';
import LargeStarIcon from 'icons/LargeStarIcon';
import { useRouter } from 'next-nprogress-bar';

export function ProcessLearner({
  courseInfo,
}: Readonly<{
  courseInfo: CourseInfo;
}>) {
  const router = useRouter();
  const isShowPreviewBtn = usePermissions().canShowPreviewBtn(courseInfo);

  const totalLectures = courseInfo?.totalLectures ?? 0;

  const countCompletedLectures = courseInfo?.countCompletedLectures ?? 0;

  const percentage = Math.round((countCompletedLectures * 100) / totalLectures);
  const onViewResultCourse = () => {};

  const getContentDropdown = () => {
    return (
      <div className={'w-[382px] rounded-[12px]'}>
        <div className={'flex items-center gap-[16px] p-[24px]'}>
          <div className={'right-content flex items-center'}>
            <Progress
              type="circle"
              percent={percentage}
              size={40}
              format={() => (
                <div className={'flex items-center justify-center'}>
                  <Goal />
                </div>
              )}
            />
          </div>
          {percentage === 100 ? (
            <p>
              Bạn đã hoàn thành khóa học. Xem thành tích của bạn{' '}
              <span className={'cursor-pointer text-primary underline'} onClick={onViewResultCourse}>
                {' '}
                tại đây
              </span>
            </p>
          ) : (
            <p>
              Bạn đã hoàn thành <span className={'font-bold'}>{isNaN(percentage) ? 0 : percentage}%</span> khóa học
            </p>
          )}
        </div>
        {percentage !== 100 && (
          <div className={'content flex flex-col gap-[12px] bg-[#F2F2F4] p-[24px]'}>
            <div className={'flex gap-4'}>
              <div className={'h-[12px] w-[12px]'}>
                <span className={'mt-[6px] inline-block'}>
                  <LargeStarIcon />
                </span>
              </div>
              <p>
                Hoàn thành khóa học để nhận: chứng chỉ <span className={'font-bold'}>Sơ cấp Lịch sử từ</span> ABC
                Academy
              </p>
            </div>
            <div className={'flex gap-4'}>
              <div className={'h-[12px] w-[12px]'}>
                <span className={'mt-[6px] inline-block'}>
                  <LargeStarIcon />
                </span>
              </div>
              <p>40 điểm kinh nghiệm tài khoản (cần 30 điểm nữa để lên cấp 12)</p>
            </div>
            <div className={'flex gap-4'}>
              <div className={'h-[12px] w-[12px]'}>
                <span className={'mt-[6px] inline-block'}>
                  <LargeStarIcon />
                </span>
              </div>
              <p>Hoàn thành 2 khóa học cùng trình độ Cơ bản để mở khóa bản đồ mới</p>
            </div>
          </div>
        )}
      </div>
    );
  };

  const menuDropdown: MenuProps['items'] = [
    {
      label: getContentDropdown(),
      key: '0',
    },
  ];
  return (
    <>
      {isShowPreviewBtn && (
        <Button onClick={() => router.back()} type={'primary'} size={'small'}>
          Chuyển về chế độ chỉnh sửa
        </Button>
      )}
      <div className={'flex items-center gap-5'}>
        <Dropdown
          menu={{ items: menuDropdown }}
          overlayClassName={'drop-down-goal'}
          trigger={['click']}
          className={'cursor-pointer'}
        >
          <div className={'right-content flex items-center'}>
            <Progress
              type="circle"
              percent={percentage}
              size={40}
              format={() => (
                <div className={'flex items-center justify-center'}>
                  <Goal />
                </div>
              )}
            />
            <p className={'ml-2 mr-4'}>Tiến trình học của bạn</p>
            <DownOutlined />
          </div>
        </Dropdown>
      </div>
    </>
  );
}

export default ProcessLearner;
