'use client';

import { CourseInfo } from '@/features/courses';
import { routePaths } from 'config';
import { ImageLogo } from 'images';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import './index.scss';

const ProcessLearner = dynamic(() => import('./ProcessLearner'), {
  ssr: false,
});

type LearnerHeaderProps = {
  courseInfo: CourseInfo;
};
export const LearnerHeader = (props: LearnerHeaderProps) => {
  const { courseInfo } = props;

  return (
    <div className={'learner-header sticky top-0 h-[72px] px-[24px] py-[16px] shadow-2xl'}>
      <div className={'flex items-center justify-between'}>
        <div className={'left-content'}>
          <div className={'flex w-full items-center gap-6'}>
            <Link href={routePaths.profile.path}>
              <Image width={130} height={32} src={ImageLogo} alt="logo" />
            </Link>
            <div className={'h-[16px] w-[1px] bg-black'} />
            <Link href={`${routePaths.profile.children.course.path}/${courseInfo?.id}`}>
              <p className={'text-black hover:text-blue-500'}>{courseInfo?.courseName}</p>
            </Link>
          </div>
        </div>
        <div className={'flex items-center gap-4'}>
          <ProcessLearner courseInfo={courseInfo} />
        </div>
      </div>
    </div>
  );
};
