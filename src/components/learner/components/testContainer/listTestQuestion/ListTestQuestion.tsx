import { CheckedCircle, ErrorCircle } from 'icons';
import IconCollapsePopup from 'icons/IconCollapsePopup';
import IconQuestionDone from 'icons/IconQuestionDone';
import IconQuestionNotDone from 'icons/IconQuestionNotDone';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setLearnerTest } from 'reducer/courseReducer/testSlice';
import './listTestQuestion.scss';

export const ListTestQuestion = ({ test, user_answers, showResultTest }: any) => {
  const dispatch = useDispatch();
  const learnerTest = useSelector((state: any) => state.test.learnerTest);
  const [objectAnswer, setObjectAnswer] = useState<any>({});
  const [points, setPoints] = useState(0);
  const [isShow, setIsShow] = useState(false);

  useEffect(() => {
    if (test.id && user_answers?.length) {
      let temp: any = {};
      let tempPoints = 0;
      user_answers.forEach((item: any) => {
        temp = {
          ...temp,
          [item.question_id]: item.point,
        };
        if (item.point) tempPoints++;
      });
      setObjectAnswer(temp);
      setPoints(tempPoints);
    }
  }, [test, user_answers]);

  useEffect(() => {
    if (user_answers?.length && !learnerTest.startAt && !showResultTest) {
      setIsShow(true);
    }
  }, [showResultTest]);

  const goToQuestion = (index: number) => {
    dispatch(
      setLearnerTest({
        ...learnerTest,
        currentIndex: index,
      }),
    );
  };

  const onClosePopup = () => {
    setIsShow(false);
  };

  return (
    <React.Fragment>
      <button title={'Danh sách câu hỏi'} className={'hover:text-primary'} onClick={() => setIsShow(true)}>
        Danh sách câu hỏi
      </button>
      {isShow && (
        <div className="list-test-question">
          <div className="flex justify-between">
            <p className="font-bold">
              {user_answers?.length
                ? `Câu hỏi (Trả lời đúng ${points}/${test.questions.length})`
                : `Câu hỏi (Đã trả lời ${Object.keys(learnerTest.answers).length}/${test.questions.length})`}
            </p>
            <span onClick={onClosePopup}>
              <IconCollapsePopup />
            </span>
          </div>
          <div className="list-test-question__wrap-items">
            {test.questions.map((item: any, key: number) => (
              <div className="list-test-question__item" key={key} onClick={() => goToQuestion(key)}>
                {user_answers?.length ? (
                  objectAnswer[item.id] ? (
                    <CheckedCircle />
                  ) : (
                    <ErrorCircle />
                  )
                ) : learnerTest.answers[item.id] ? (
                  <IconQuestionDone />
                ) : (
                  <IconQuestionNotDone />
                )}
                <span className="list-test-question__item-name">{item.question_name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </React.Fragment>
  );
};
