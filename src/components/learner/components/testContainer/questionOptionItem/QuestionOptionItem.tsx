import { Checkbox, Image, Radio, Space } from 'antd';
import { getColorForAnswer } from 'components/learner/convert';
import { answerMapping } from 'constants/index';
import { QuestionOption } from 'type';

type QuestionOptionItemProps = {
  unique: string;
  question: QuestionOption;
  isCheckAnswer: boolean;
  index: number;
  correct_answer: number[];
  userAnswers: number[];
  isMultipleAnswer: boolean;
  handleAnswer: (value: number) => void;
  questionLength: number;
};
const QuestionOptionItem = ({
  unique,
  question,
  isCheckAnswer,
  index,
  correct_answer,
  userAnswers,
  isMultipleAnswer,
  handleAnswer,
  questionLength,
}: QuestionOptionItemProps) => {
  const maxHeight = questionLength <= 2 && !question.option_thumbnail_image;

  const isUserChecked = !!userAnswers?.length && userAnswers.includes(index + 1);

  return (
    <div className={`flex items-center gap-2`}>
      <p>{answerMapping[question.option_index]}. </p>
      <div
        className={`flex w-full cursor-pointer items-center rounded-sm px-4 py-[12px] ${
          isCheckAnswer ? 'border-primary-500' : 'bg-neutral-50'
        }`}
        onClick={() => handleAnswer(index + 1)}
        // style={{
        //   background: getColorForAnswer(index + 1, correct_answer, userAnswers).bgColor,
        //   borderColor: getColorForAnswer(index + 1, correct_answer, userAnswers).borderColor,
        // }}
      >
        <div className={`flex gap-[10px] ${isCheckAnswer ? 'cursor-not-allowed' : ''} `}>
          <Space size={10} className={'w-full'}>
            {isMultipleAnswer ? (
              <Checkbox
                checked={userAnswers.includes(index + 1)}
                className={
                  isCheckAnswer && userAnswers.includes(index + 1)
                    ? getColorForAnswer(index + 1, correct_answer, userAnswers).color
                    : ''
                }
              />
            ) : (
              <Radio checked={isUserChecked} id={`${unique}-${question.option_index}`} />
            )}
            {question.option_thumbnail_image && (
              <Image
                className={'block object-cover'}
                alt="option thumbnail image"
                src={question.option_thumbnail_image}
                width={90}
                height={60}
              />
            )}
            <div className={`overflow-y-auto ${maxHeight ? 'max-h-[120px]' : 'max-h-[80px]'}`}>
              <p className={'text-left text-base'}>{question.option_name}</p>
            </div>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default QuestionOptionItem;
