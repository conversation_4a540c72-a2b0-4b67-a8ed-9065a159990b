'use client';

import { CourseInfo, LectureDetail, Section, UserTestResult } from '@/features/courses';
import { SectionType } from 'config/constant';
import dynamic from 'next/dynamic';
import { useState } from 'react';
import { Test } from 'type';
import './testContainer.scss';

const ResultTest = dynamic(() => import('./resultTest'), {
  ssr: false,
});

const CurrentTestQuestion = dynamic(() => import('./currentTestQuestion'), {
  ssr: false,
  loading: () => <div className={'flex h-full w-full items-center justify-center pt-12'}><PERSON>ang tải bài kiểm tra...</div>,
});

export type TestContainerProps = {
  lectureTestData?: Test;
  courseInfo: CourseInfo;
  currentSection: Section;
  selectedLecture: LectureDetail | undefined;
};
export const TestContainer = ({ courseInfo, currentSection, selectedLecture, lectureTestData }: TestContainerProps) => {
  const { id: lectureId } = selectedLecture || {};
  const [showResultTest, setShowResultTest] = useState<boolean>(false);
  const [userAnswers, setUserAnswers] = useState<UserTestResult[]>([]);

  let test = currentSection.test;

  const isSectionTest = currentSection.sectionTypeId === SectionType.Test;

  if (!isSectionTest) {
    test = currentSection.lectures.find((lecture) => lecture.testId !== null)?.test ?? null;
  }

  const testQuestions = lectureTestData?.questions || [];

  if (testQuestions.length <= 0 && currentSection.sectionTypeId === SectionType.Test) {
    return <></>;
  }

  return (
    <div className={'test-container h-full'}>
      <div className="bg-black px-2 py-4 text-white">{!selectedLecture ? 'Kiểm tra cuối khóa học' : ''}&nbsp;</div>
      <div className="test-container-content">
        <div className="container h-full text-center">
          <div className="test-content-middle">
            {showResultTest ? (
              <ResultTest
                courseInfo={courseInfo}
                currentSection={currentSection}
                test={isSectionTest ? lectureTestData : (test as any)}
                userAnswers={userAnswers}
                setShowResultTest={setShowResultTest}
                setUserAnswers={setUserAnswers}
              />
            ) : (
              <CurrentTestQuestion
                userAnswers={userAnswers}
                setUserAnswers={setUserAnswers}
                test={lectureTestData!}
                lecture_id={lectureId}
                currentSection={currentSection}
                showResultTest={showResultTest}
                setShowResultTest={setShowResultTest}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestContainer;
