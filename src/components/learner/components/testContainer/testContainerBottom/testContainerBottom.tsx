import { UserTestResult } from '@/features/courses/types';
import { RootState } from '@/store/store';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import ChaptersIcon from 'icons/ChaptersIcon';
import IconChevronLeft from 'icons/IconChevronLeft';
import IconChevronRight from 'icons/IconChevronRight';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setLearnerTest } from 'reducer/courseReducer/testSlice';
import { Test } from 'type';
import { ListTestQuestion } from '../listTestQuestion/ListTestQuestion';
import TestTimeClock from '../testTimeClock/testTimeClock';
import './testContainerBottom.scss';

const TestConfirmPopup = dynamic(() => import('../testConfirmPopup'), {
  ssr: false,
});
const TestTimeoutPopup = dynamic(() => import('../testTimeoutPopup'), {
  ssr: false,
});
export const TestContainerBottom = ({
  test,
  userAnswers,
  doSubmit,
  isLoadingSubmit,
  showResultTest,
}: {
  test: Test;
  userAnswers: UserTestResult[];
  doSubmit: () => void;
  isLoadingSubmit: boolean;
  showResultTest: boolean;
}) => {
  const dispatch = useDispatch();
  const learnerTest = useSelector((state: RootState) => state.test.learnerTest);
  const [timeDoTest, setTimeDoTest] = useState(0);
  const [countQuestionsNotDone, setCountQuestionsNotDone] = useState(0);
  const [isTimeOut, setIsTimeOut] = useState(false);
  const { currentIndex, showPopupConfirmSubmit } = learnerTest;

  useEffect(() => {
    setTimeDoTest(1);
  }, [learnerTest.startAt]);

  const goToQuestion = (index: number) => {
    dispatch(
      setLearnerTest({
        ...learnerTest,
        currentIndex: index,
      }),
    );
  };
  const setIsShow = (value: boolean) => {
    dispatch(
      setLearnerTest({
        ...learnerTest,
        showPopupConfirmSubmit: value,
      }),
    );
  };

  const onSubmit = () => {
    let countQuestionsDone = 0;
    Object.keys(learnerTest.answers).forEach((index) => {
      if (learnerTest.answers[index]?.length) countQuestionsDone++;
    });
    setCountQuestionsNotDone(test.questions.length - countQuestionsDone);
    setIsShow(true);
  };
  const { handleOpenChapterDrawer } = useChapterDrawer();
  return (
    <div className="test-container-bottom">
      <div className="test-container-bottom__progress-bar">
        <div
          className="test-container-bottom__progress-bar-done"
          style={{ width: `${Math.floor((currentIndex / test.questions.length) * 100)}%` }}
        ></div>
      </div>
      <div className="grid w-full grid-cols-3 items-center">
        <div>
          {test.has_limit_time && learnerTest.startAt && timeDoTest ? (
            <span>
              Thời gian : <TestTimeClock test={test} setIsTimeOut={setIsTimeOut} timeDoTest={timeDoTest} /> /{' '}
              {test.limit_time}
              :00
            </span>
          ) : null}
          <span className="ml-10">
            Câu hỏi {currentIndex + 1} / {test.questions.length}
          </span>
        </div>
        <div>
          <div className="flex justify-between">
            <div
              className={`flex ${currentIndex === 0 ? 'disable-click' : ''}`}
              onClick={() => goToQuestion(currentIndex - 1)}
            >
              <IconChevronLeft /> Câu hỏi trước
            </div>
            <ListTestQuestion test={test} user_answers={userAnswers} showResultTest={showResultTest} />
            <div
              className={`flex ${currentIndex === test.questions.length - 1 ? 'disable-click' : ''}`}
              onClick={() => goToQuestion(currentIndex + 1)}
            >
              Câu hỏi sau <IconChevronRight />
            </div>
          </div>
        </div>
        <div className="inline-flex items-center justify-end">
          {learnerTest.startAt && timeDoTest ? (
            <button className="button-submit-test mr-4" onClick={onSubmit} disabled={isLoadingSubmit}>
              Nộp bài
            </button>
          ) : null}
          <button
            title={'Xem danh sách chương'}
            className="mr-8 inline-flex text-white hover:text-primary"
            onClick={handleOpenChapterDrawer}
          >
            <ChaptersIcon />
          </button>
        </div>
      </div>
      <TestConfirmPopup
        countQuestionsNotDone={countQuestionsNotDone}
        isShow={showPopupConfirmSubmit}
        setIsShow={setIsShow}
        doSubmit={doSubmit}
      />
      <TestTimeoutPopup isTimeOut={isTimeOut} setIsTimeOut={setIsTimeOut} doSubmit={doSubmit} />
    </div>
  );
};
