import { CourseInfo, Section } from '@/features/courses';
import { UserTestResult } from '@/features/courses/types/test.type';
import { RootState } from '@/store/store';
import { notification } from 'antd';
import { SectionType, routePaths } from 'config';
import dayjs from 'dayjs';
import { useQuerySearch } from 'hooks';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import ChaptersIcon from 'icons/ChaptersIcon';
import { useRouter } from 'next-nprogress-bar';
import { useDispatch, useSelector } from 'react-redux';
import { setLearnerTest } from 'reducer/courseReducer/testSlice';
import { Test } from 'type';
import './resultTest.scss';

export const ResultTest = ({
  courseInfo: course,
  currentSection,
  test,
  userAnswers,
  setShowResultTest,
  setUserAnswers,
}: {
  courseInfo: CourseInfo;
  currentSection: Section;
  test: Test;
  userAnswers: UserTestResult[];
  setShowResultTest: (showResultTest: boolean) => void;
  setUserAnswers: (userAnswers: UserTestResult[]) => void;
}) => {
  const dispatch = useDispatch();
  const currentIndex = currentSection.sortIndex;
  const query = useQuerySearch();
  const router = useRouter();
  const learnerTest = useSelector((state: RootState) => state.test.learnerTest);
  const { handleOpenChapterDrawer } = useChapterDrawer();

  const numberOfCorrectAnswers = userAnswers.filter(
    (item) => test.questions.includes(item.questionId) && item.point === 1,
  ).length;

  const doTestAgain = () => {
    setUserAnswers([]);
    setShowResultTest(false);
    dispatch(
      setLearnerTest({
        test_id: test.id,
        currentIndex: 0,
        answers: {},
        startAt: dayjs().valueOf(),
        isDoingTest: 1,
      }),
    );
  };

  const onViewPreviousLecture = () => {
    if (currentSection.sectionTypeId === SectionType.Test) {
      const firstSection = course.sections?.[0];
      if (firstSection && firstSection.lectures && firstSection.lectures.length > 0) {
        const lectureId = firstSection.lectures[0].id;
        return router.push(
          `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${firstSection.id}&lectureId=${lectureId}`,
        );
      }
      return router.push(
        `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${course.sections?.[0].id}`,
      );
    } else {
      router.push(
        `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?lectureId=${currentSection.lectures[0]?.id}&sectionId=${currentSection.id}`,
      );
    }
  };

  const onViewNextLecture = () => {
    if (currentSection.sectionTypeId === SectionType.Default) {
      const currentLecture = currentSection.lectures.find((lecture) => lecture.id?.toString() === query?.lectureId);
      if (!currentLecture) return;
      const nextLecture = currentSection.lectures.find((lecture) => lecture.sortIndex > currentLecture.sortIndex);
      if (nextLecture) {
        const nextPathLecture = `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${currentSection.id}&lectureId=${nextLecture.id}`;
        router.push(nextPathLecture);
        return;
      }
      const nextSection = course.sections?.find((section) => section.sortIndex > currentIndex);
      if (!nextSection) {
        return notification.error({
          message: 'Đây là bài học cuối',
        });
      }
      // next section is test or learning outcome
      if (nextSection.sectionTypeId !== SectionType.Default) {
        router.push(
          `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${nextSection.id}`,
        );
        return;
      }
      if (!nextSection?.lectures || nextSection.lectures.length <= 0) {
        return notification.error({
          message: 'Next Section has not lecture',
        });
      }
      const nextPathSection = `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${nextSection.id}&lectureId=${nextSection.lectures[0].id}`;
      return router.push(nextPathSection);
    }
    const nextSection2 = course.sections?.find((section) => section.sortIndex > currentIndex);
    if (!nextSection2) {
      return notification.error({
        message: 'Đây là bài học cuối',
      });
    }
    if (!nextSection2?.lectures || nextSection2.lectures.length <= 0) {
      router.push(
        `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${nextSection2.id}`,
      );
      return;
    }
    const nextPathSection = `${routePaths.learner.path.replace(':courseId', course.id?.toString())}?sectionId=${nextSection2.id}&lectureId=${nextSection2.lectures[0].id}`;
    return router.push(nextPathSection);
  };

  return (
    <div className="result-test">
      <div className="bg-black px-2 py-4 text-white">&nbsp;</div>
      <div className="container h-full text-center">
        <div className="result-test-content">
          {numberOfCorrectAnswers >= test.min_correct_answer ? (
            <>
              <div className="result-test__title">Chúc mừng!</div>
              <div className="result-test__description">Bạn đã đạt đủ điểm trong bài kiểm tra</div>
              <p>
                (Bạn trả lời đúng {numberOfCorrectAnswers}/{test?.questions?.length ?? 0} câu)
              </p>
              <div className="mt-4">
                <button
                  className="btn-custom-outline"
                  onClick={() => {
                    dispatch(
                      setLearnerTest({
                        ...learnerTest,
                        startAt: null,
                        currentIndex: 0,
                        isDoingTest: 0,
                      }),
                    );
                    setShowResultTest(false);
                  }}
                >
                  Xem lại đáp án
                </button>
                <button className="btn-custom ml-4" onClick={onViewNextLecture}>
                  Chuyển tới bài học tiếp theo
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="result-test__title">Rất tiếc!</div>
              <div className="result-test__description">
                Bạn chưa đạt đủ điểm cho bài kiểm tra này <br />
                Cần ít nhất {test.min_correct_answer} câu trả lời đúng để hoàn thành bài kiểm tra
              </div>
              <p>
                (Bạn trả lời đúng {numberOfCorrectAnswers}/{test.questions?.length ?? 0} câu)
              </p>
              <div className="mt-4">
                <button className="btn-custom-outline" onClick={onViewPreviousLecture}>
                  Xem lại bài học trước
                </button>
                <button className="btn-custom ml-4" onClick={doTestAgain}>
                  Làm lại bài kiểm tra
                </button>
              </div>
            </>
          )}
        </div>
      </div>
      <div className="test-container-bottom">
        <div className="test-container-bottom__progress-bar"></div>
        <div className="inline-flex w-full items-center justify-end">
          <button
            title={'Xem danh sách chương'}
            className="mr-8 inline-flex text-white hover:text-primary"
            onClick={handleOpenChapterDrawer}
          >
            <ChaptersIcon />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResultTest;
