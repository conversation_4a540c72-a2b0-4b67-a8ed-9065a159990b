import { LectureInteract, Section } from '@/features/courses';
import { genSectionName } from '@/utils/genSectionName';
import { LectureType } from 'config';

export function LearnerSectionName(
  props: Readonly<{
    section?: Section;
    lectureType?: LectureType;
    selectedInteractionVideo?: LectureInteract;
  }>,
) {
  const { section: chapter, lectureType, selectedInteractionVideo } = props;
  const sectionName = genSectionName({
    section: chapter,
    lectureType,
    selectedInteractionVideo,
  });
  return <div className={'absolute top-0 p-[16px] font-bold text-white'}>{sectionName}</div>;
}
