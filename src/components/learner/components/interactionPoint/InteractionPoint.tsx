import { LectureDetail, LectureInteract } from '@/features/courses/types';
import { timeStampToSecond } from 'utils/course/dateTime';

type InteractionPointProps = {
  totalTime: number;
  currentTime: number;
  selectedLecture: LectureDetail | undefined;
  setSelectedInteractionVideo: (value: LectureInteract) => void;
};
const InteractionPoint = ({
  totalTime,
  selectedLecture,
  setSelectedInteractionVideo,
  currentTime,
}: InteractionPointProps) => {
  if (!selectedLecture?.lectureInteracts) return null;

  return (
    <>
      {selectedLecture?.lectureInteracts?.map((lectureInteraction) => {
        const interactionSecond = timeStampToSecond(lectureInteraction.start_at);
        const position = (interactionSecond * 100) / totalTime;
        return (
          <div
            key={lectureInteraction.id}
            className={'absolute top-[-6px] z-[9999999999] h-2 w-1 bg-yellow-500'}
            style={{ left: `${position}%` }}
            onClick={() => {
              if (interactionSecond < currentTime) {
                setSelectedInteractionVideo(lectureInteraction);
              }
            }}
          />
        );
      })}
    </>
  );
};

export default InteractionPoint;
