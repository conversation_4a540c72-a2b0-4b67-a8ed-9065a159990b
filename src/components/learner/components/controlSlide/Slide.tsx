import { SlideItem } from '@/features/courses/types';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Space } from 'antd';

export function Slide({
  currentSlideIdx,
  setCurrentSlideIdx,
  handleNextSlide,
  slideItems,
  isPreventNextSlide,
}: Readonly<{
  currentSlideIdx: number;
  setCurrentSlideIdx: (slideIdx: number) => void;
  slideItems: SlideItem[];
  isPreventNextSlide: boolean;
  handleNextSlide: () => void;
}>) {
  return (
    <Space size={32} className={'flex w-full justify-center'}>
      {currentSlideIdx > 0 ? (
        <div
          className={'flex cursor-pointer gap-3 text-white'}
          onClick={() => {
            setCurrentSlideIdx(currentSlideIdx - 1);
          }}
        >
          <LeftOutlined />
          <p>Slide trước</p>
        </div>
      ) : (
        <div className={'w-[80px]'}></div>
      )}
      {currentSlideIdx < slideItems.length - 1 ? (
        <button
          className={'flex gap-3 text-white'}
          disabled={isPreventNextSlide}
          style={{ cursor: isPreventNextSlide ? 'not-allowed' : 'pointer' }}
          onClick={handleNextSlide}
        >
          <p>Slide sau</p>
          <RightOutlined />
        </button>
      ) : (
        <div className={'w-[80px]'}></div>
      )}
    </Space>
  );
}
