'use client';

import { LectureDetail, SlideItem } from '@/features/courses/types';
import { Space, Switch, message } from 'antd';
import { SegmentSlide } from 'components/learner/components/controlSlide/SegmentSlide';
import { SegmentSlideActive } from 'components/learner/components/controlSlide/SegmentSlideActive';
import { Slide } from 'components/learner/components/controlSlide/Slide';
import { getTitleSlide } from 'components/learner/components/controlSlide/genTitleSlide';
import ShowContentHyperNote from 'components/learner/components/showContentHyperNote/ShowContentHyperNote';
import { checkAnswerQuestion } from 'components/learner/convert';
import { HyperNoteBody } from 'components/learner/type';
import { InteractionType } from 'constants/index';
import { ShowOutline } from 'icons';
import { ReactNode } from 'react';

type ControlSlideProps = {
  selectedLecture: LectureDetail | undefined | null;
  setCurrentSlideIdx: (idx: number) => void;
  currentSlideIdx: number;
  isShowPreviewSlide: boolean;
  setIsShowPreviewSlide: (value: boolean) => void;
  hyperData: HyperNoteBody[];
  totalTime: number;
  isPreviewMode: boolean;
  slideItems: SlideItem[];
  volumeComponent: ReactNode;
  audioComponent: ReactNode;
  chapterSidebarComponent: ReactNode;
};
const ControlSlide = (props: ControlSlideProps) => {
  const {
    selectedLecture,
    hyperData: hypersInLecture,
    volumeComponent,
    audioComponent,
    chapterSidebarComponent,
    currentSlideIdx,
    setCurrentSlideIdx,
    isShowPreviewSlide,
    setIsShowPreviewSlide,
    totalTime,
    isPreviewMode,
    slideItems,
  } = props;

  const isPreventNextSlide = [InteractionType.Explore, InteractionType.Situation].includes(
    slideItems[currentSlideIdx]?.slide_item_type_id,
  );

  const handleNextSlide = () => {
    const currentSlide = slideItems[currentSlideIdx];
    if (isPreventNextSlide || !currentSlide) return;
    if (!currentSlide.question?.id) {
      setCurrentSlideIdx(currentSlideIdx + 1);
      return;
    }
    if (checkAnswerQuestion(currentSlideIdx, slideItems[currentSlideIdx], selectedLecture!)) {
      setCurrentSlideIdx(currentSlideIdx + 1);
      return;
    }
    message.warning('Vui lòng trả lời câu hỏi');
  };

  return (
    <>
      {/*<div className={'seek seek-learner absolute top-0 left-0 right-0 h-[3px]'}></div>*/}
      <SegmentSlide slideItems={slideItems} />
      <SegmentSlideActive slideItems={slideItems} currentSlideIdx={currentSlideIdx} />
      <div className={'absolute left-0 right-0 top-[-4px] z-10 h-[4px]'}>
        <ShowContentHyperNote
          hyperData={hypersInLecture}
          totalTime={totalTime}
          type={'slide'}
          slideItems={slideItems}
          isShowHyperNote={!isPreviewMode}
        />
      </div>
      <div className={'control flex h-full items-center justify-between px-[20px]'}>
        <Space size={30} className={'flex h-full w-full'}>
          {audioComponent}
          {volumeComponent}
          <span className={'slide-list text-white'}>
            {currentSlideIdx + 1}/{slideItems.length}
          </span>

          <button className={'flex items-center gap-4 truncate text-sm text-white outline-none'}>
            {slideItems.length > 0 && (
              <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                <circle cx="2" cy="2" r="2" fill="white" />
              </svg>
            )}

            {getTitleSlide(slideItems[currentSlideIdx])}
            {slideItems.length > 0 && (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.46967 17.5303C9.17678 17.2374 9.17678 16.7626 9.46967 16.4697L13.9393 12L9.46967 7.53033C9.17678 7.23744 9.17678 6.76256 9.46967 6.46967C9.76256 6.17678 10.2374 6.17678 10.5303 6.46967L15.5303 11.4697C15.8232 11.7626 15.8232 12.2374 15.5303 12.5303L10.5303 17.5303C10.2374 17.8232 9.76256 17.8232 9.46967 17.5303Z"
                  fill="white"
                />
              </svg>
            )}
          </button>
        </Space>

        <Slide
          currentSlideIdx={currentSlideIdx}
          setCurrentSlideIdx={setCurrentSlideIdx}
          slideItems={slideItems}
          isPreventNextSlide={isPreventNextSlide}
          handleNextSlide={handleNextSlide}
        />

        <Space size={24} className={'flex w-full items-center justify-end'}>
          <Space size={8} className={'flex items-center'}>
            <ShowOutline />
            <p className={'text-white'}>Hiển thị outline</p>
            <Switch
              className={`${isShowPreviewSlide ? '' : 'bg-[#ffffff33]'}`}
              onChange={(checked: boolean) => setIsShowPreviewSlide(checked)}
              checked={isShowPreviewSlide}
            />
          </Space>
          {chapterSidebarComponent}
        </Space>
      </div>
    </>
  );
};

export default ControlSlide;
