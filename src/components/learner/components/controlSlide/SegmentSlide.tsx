import { SlideItem } from '@/features/courses/types';

export function SegmentSlide({ slideItems }: Readonly<{ slideItems: SlideItem[] }>) {
  return (
    <div className={'seek absolute left-0 right-0 top-0 h-[3px]'}>
      <div className={'flex gap-1'}>
        {slideItems.map((item) => {
          return (
            <div
              key={item.id}
              className={'z-10 h-[3px] bg-white opacity-50'}
              style={{ width: `${100 / slideItems.length}%` }}
            />
          );
        })}
      </div>
    </div>
  );
}
