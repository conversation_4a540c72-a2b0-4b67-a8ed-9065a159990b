import { SlideItem } from '@/features/courses/types';
import { InteractionType } from 'constants/index';

export const getTitleSlide = (slideItem: SlideItem) => {
  const slideItems = [
    {
      slideType: InteractionType.Explore,
      label: '<PERSON>h<PERSON><PERSON> phá thêm',
    },
    {
      slideType: InteractionType.Question,
      label: '<PERSON>âu hỏi nhanh',
    },
    {
      slideType: InteractionType.Situation,
      label: '<PERSON>ên hệ thực tế',
    },
  ];
  const title = slideItems.find((slide) => slide?.slideType === slideItem?.slide_item_type_id)?.label;
  if (!title) return slideItem?.slide_item_name;
  return title;
};
