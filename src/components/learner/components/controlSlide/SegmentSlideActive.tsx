import { SlideItem } from '@/features/courses/types';

export function SegmentSlideActive({
  slideItems,
  currentSlideIdx,
}: Readonly<{ slideItems: SlideItem[]; currentSlideIdx: number }>) {
  return (
    <div className={'seek absolute left-0 right-0 top-0 h-[3px]'}>
      <div className={'flex gap-1'}>
        {slideItems.map((item, index) => {
          return (
            <div key={item.id} className={'relative'} style={{ width: `${100 / slideItems.length}%` }}>
              {currentSlideIdx >= index && (
                <div key={item.id} className={'absolute top-0 h-[3px] w-full bg-white opacity-90'} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
