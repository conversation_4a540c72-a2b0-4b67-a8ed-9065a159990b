import { SlideItem } from '@/features/courses/types';
import { Divider, Space } from 'antd';
import { HyperNoteBody } from 'components/learner/type';
import { LectureType } from 'config';
import { secondToHHMMSS } from 'utils';

type ContentHyperProps = {
  hyperNotes: HyperNoteBody[];
  slideItems: SlideItem[];
};

const ContentHyper = ({ hyperNotes, slideItems }: ContentHyperProps) => {
  return (
    <div className={'max-h-[350px] w-[500px] overflow-hidden overflow-y-auto p-[16px]'}>
      {hyperNotes.map((hyper, index) => {
        const isHyper = hyper.type_id === 1;
        return (
          <div key={hyper.id}>
            <Space size={8} direction={'vertical'} className={'w-full'}>
              <Space className={'w-full'} size={8}>
                <p className={'text-xs text-[#4F5261]'}>{hyper.extra_info?.course_name}:</p>
                {hyper.extra_info?.lecture_type_id === LectureType.Video ? (
                  <>
                    <p className={'text-xs text-[#4F5261]'}>{hyper.extra_info?.lecture_name}</p>
                    <p className={'text-xs text-[#4F5261]'}>
                      ({secondToHHMMSS(hyper.extra_info?.current_time || 0, 'MMSS')})
                    </p>
                  </>
                ) : (
                  <p className={'text-xs text-[#4F5261]'}>{`Slide ${
                    slideItems.findIndex((item: SlideItem) => item.id === hyper.extra_info?.slide?.slide_id) + 1
                  }`}</p>
                )}
              </Space>
              {!isHyper && <p className={'text-truncate-2 w-[512px] font-bold'}>{hyper.title}</p>}
              <p
                className={'text-truncate-2 w-[512px]'}
                dangerouslySetInnerHTML={{ __html: (isHyper ? hyper.title : hyper.answer) as string }}
              />
            </Space>
            {index !== hyperNotes.length - 1 && <Divider className={'my-1'} />}
          </div>
        );
      })}
    </div>
  );
};

export default ContentHyper;
