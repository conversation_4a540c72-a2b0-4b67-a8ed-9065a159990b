import { SlideItem } from '@/features/courses/types';
import Popover from 'antd/es/popover';
import ContentHyper from 'components/learner/components/showContentHyperNote/ContentHyper';
import { HYPERNOTE_TIME_STEP } from 'components/learner/constant';
import { handleGetHypersInSameSlide, handleHyperData } from 'components/learner/convert';
import { HyperNoteBody } from 'components/learner/type';
import { memo, useMemo } from 'react';

type ShowContentHyperNoteProps = {
  hyperData: HyperNoteBody[];
  totalTime: number;
  isShowHyperNote: boolean;
  type: 'slide' | 'video';
  slideItems?: SlideItem[];
};
const ShowContentHyperNote = ({
  hyperData,
  totalTime,
  type,
  slideItems,
  isShowHyperNote,
}: ShowContentHyperNoteProps) => {
  const getNotePointPositionForVideo = () => {
    if (!isShowHyperNote) return null;

    return handleHyperData(hyperData, HYPERNOTE_TIME_STEP).map((hypers: HyperNoteBody[], index: number) => {
      const currentHyper = hypers[0];
      let position = ((currentHyper.extra_info?.current_time ?? 0) * 100) / totalTime;
      return (
        <Popover
          key={index}
          placement={'top'}
          content={() => <ContentHyper hyperNotes={hypers} slideItems={slideItems!} />}
          trigger={'click'}
        >
          <div
            className={'absolute top-[-6px] z-[9999999999] h-2 w-1 bg-yellow-500'}
            style={{ left: `${position}%` }}
          />
        </Popover>
      );
    });
  };

  const notePointPositionForVideo = useMemo(() => getNotePointPositionForVideo(), [hyperData]);

  const getNotePointPositionForSlide = () => {
    if (!isShowHyperNote) return null;

    return handleGetHypersInSameSlide(hyperData).map((hypers: { index: number; items: HyperNoteBody[] }) => {
      const currentHyper = hypers.items[0];
      const widthSlide = 100 / slideItems?.length!;
      const slideIdx = slideItems!.findIndex((item: SlideItem) => item.id === currentHyper.extra_info?.slide.slide_id);
      if (slideIdx === -1) return <></>;
      let position = (slideIdx + 1) * widthSlide - widthSlide / 2;
      return (
        <Popover
          key={`hyper-${hypers.index}`}
          placement={'top'}
          content={() => <ContentHyper hyperNotes={hypers.items} slideItems={slideItems!} />}
          trigger={'click'}
        >
          <div className={'absolute top-[2px] z-[100] h-2 w-1 bg-yellow-500'} style={{ left: `${position}%` }} />
        </Popover>
      );
    });
  };

  return <span>{type === 'slide' ? getNotePointPositionForSlide() : notePointPositionForVideo}</span>;
};

export default memo(ShowContentHyperNote);
