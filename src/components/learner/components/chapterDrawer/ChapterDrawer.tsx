'use client';

import { CourseInfo } from '@/features/courses';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import CollapseLearnerIcon from 'icons/CollapseLearnerIcon';
import dynamic from 'next/dynamic';
import React from 'react';
import './index.scss';

type ChapterDrawerProps = {
  courseInfo: CourseInfo;
};
const ChapterCollapse = dynamic(() => import('components/learner/components/chapterCollapse/ChapterCollapse'), {
  loading: () => <div className={'text-center'}><PERSON><PERSON> tải chương học...</div>,
  ssr: false,
});
const ChapterDrawer: React.FC<ChapterDrawerProps> = ({ courseInfo }) => {
  const { handleCloseChapterDrawer, open } = useChapterDrawer();

  if (!open) return <></>;

  return (
    <div onClick={handleCloseChapterDrawer} className={'pointer-events-none fixed inset-0 z-[1002]'}>
      <div
        className={'pointer-events-auto absolute inset-0 z-[1002]'}
        style={{
          background: 'rgba(0, 0, 0, 0.45)',
        }}
      >
        <div
          onClick={(e) => e.stopPropagation()}
          className={'absolute bottom-0 right-0 top-0 z-[1000] h-full w-[378px] max-w-full drop-shadow transition-all'}
        >
          <div className={'flex h-full w-full flex-col bg-white'}>
            <div className={'border-b-gray-200 border-b p-4'}>
              <div className={'header-chapter flex justify-between'}>
                <p className={'font-bold'}>Nội dung khóa học</p>
                <span className={'cursor-pointer hover:text-primary'} onClick={handleCloseChapterDrawer}>
                  <CollapseLearnerIcon />
                </span>
              </div>
            </div>
            <ChapterCollapse courseInfo={courseInfo} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChapterDrawer;
