'use client';

import { CourseInfo, Section } from '@/features/courses/types';
import { TargetContainerTab } from 'components/learner/components/targetContainer/targetContainerTab';
import { TargetOverviewInfo } from 'components/learner/components/targetContainer/targetOverviewInfo';
import ChaptersIcon from 'icons/ChaptersIcon';
import { TargetImage } from 'images';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useState } from 'react';
import './targetContainer.scss';

const TargetContainerBottom = dynamic(() => import('../targetContainer/targetContainerBottom'), {
  ssr: false,
  loading: () => (
    <div className="fixed bottom-0 left-0 right-0 bg-black p-4 text-white">
      <div className="flex justify-between">
        <div>Kết quả học tập</div>
        <button className="mr-8 inline-flex cursor-pointer text-white">
          <ChaptersIcon />
        </button>
      </div>
    </div>
  ),
});

export const TargetContainer = ({ courseInfo, section }: { section: Section; courseInfo: CourseInfo }) => {
  const [intervalVal, setIntervalVal] = useState<NodeJS.Timeout>();

  return (
    <div onClick={() => clearInterval(intervalVal)} className="target-container">
      <Image
        src={TargetImage}
        alt={courseInfo.courseName}
        width={790}
        height={120}
        className={'h-[120px] w-full object-cover'}
      />
      <TargetContainerTab
        setIntervalValue={setIntervalVal}
        courseInfo={courseInfo as any}
        currentSection={section as any}
      />
      <div className="container">
        <h3 className="target-container__title">Mục tiêu học tập</h3>
        <p className="target-container__description">Trong chương này, bạn sẽ tìm hiểu các nội dung sau</p>
        <div>
          <TargetOverviewInfo learning_goals={section.learningGoal.learning_goal_content} />
        </div>
      </div>
      <TargetContainerBottom label="Mục tiêu học tập" />
    </div>
  );
};

export default TargetContainer;
