import { UploadedFile } from '@/features/courses';
import IconDownload from 'icons/IconDownload';
import IconStudyMaterial from 'icons/IconStudyMaterial';
import './studyMaterials.scss';

export const StudyMaterials = ({ study_materials }: { study_materials: UploadedFile[] }) => {
  return (
    <div className="bg-gray-200 mt-8 rounded-lg px-2 py-4">
      <strong>Học liệu</strong>
      <div className="study-materials-wrapper">
        {study_materials.map((item) => (
          <a className="study-materials-item" key={item.id} href={item.fileUrl} target="_blank" rel="noreferrer">
            <div className="study-materials-item-normal">
              <IconStudyMaterial />
              <div>{item.fileName}</div>
            </div>
            <div className="study-materials-item-hover">
              <div className="study-materials-item-hover__overlay"></div>
              <div className="study-materials-item-hover__text">
                <span className="inline-block">
                  <IconDownload />
                </span>
                <div>Nhấn để tải về</div>
              </div>
            </div>
          </a>
        ))}
      </div>
    </div>
  );
};

export default StudyMaterials;
