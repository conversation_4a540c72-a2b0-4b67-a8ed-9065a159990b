'use client';

import { useCourseReview } from '@/features/courses/hooks/';
import { Button, Checkbox, Col, Form, Row, Space, Typography } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import GoldCup from 'components/learner/components/review-learner/components/GoldCup';
import SelectStar from 'components/learner/components/review-learner/components/SelectStar';
import { CheckboxCustom } from 'lib';
import { useEffect, useMemo, useState } from 'react';
import ReactConfetti from 'react-confetti';
import { MasterDataCourse } from 'type';
import './index.scss';

const ReviewLearner = ({ masterData, courseId }: { masterData: MasterDataCourse; courseId: number }) => {
  const [form] = useForm();
  const [isClient, setIsClient] = useState(false);
  const [amountStar, setAmountStar] = useState<number>(5);

  const { onReviewCourse, isReviewLoading } = useCourseReview();

  const rateOptions = useMemo(() => masterData.feelings, [masterData.feelings]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const onFinish = (values: { feeling_ids: number[]; comment: string }) => {
    const comment = values.comment;
    const data = { rating: amountStar, comment };
    onReviewCourse({ courseId: courseId.toString(), data });
  };

  return (
    <div className={`review-learner center-x-y border-t-2 border-b-[#CACCD4] py-12`}>
      {isClient && <ReactConfetti width={window.innerWidth} height={window.innerHeight} />}
      <Space direction={'vertical'} size={16} className={'w-[600px] text-center'}>
        <div className={'center-x'}>
          <GoldCup />
        </div>
        <Typography.Title level={3}>Chúc mừng!</Typography.Title>
        <p
          dangerouslySetInnerHTML={{
            __html: `Bạn đã hoàn thành khóa học. </br> Hãy cho Studify biết đánh giá của bạn về khóa học nhé`,
          }}
        />
        <SelectStar amountStar={amountStar} setAmountStar={setAmountStar} />
        <Form onFinish={onFinish} form={form} layout={'vertical'}>
          <Form.Item name={'feeling_ids'}>
            <Checkbox.Group className="w-full">
              <div className="w-full">
                <p className={'mb-2 text-left'}>Cảm nhận của bạn về chất lượng bài giảng (không bắt buộc)</p>
                <Row className={'w-full'} gutter={[12, 12]}>
                  {Array.isArray(rateOptions) &&
                    rateOptions.map((item) => (
                      <Col key={item.id} span={12}>
                        <CheckboxCustom className={'right-checkbox'} key={item.id} value={item.id}>
                          {item.name}
                        </CheckboxCustom>
                      </Col>
                    ))}
                </Row>
              </div>
            </Checkbox.Group>
          </Form.Item>
          <Form.Item label={'Đánh giá về khóa học (không bắt buộc)'} name={'comment'}>
            <TextArea maxLength={300} rows={3} />
          </Form.Item>
          <Button loading={isReviewLoading} className={'w-[360px]'} type={'primary'} htmlType={'submit'}>
            Tiếp tục
          </Button>
        </Form>
      </Space>
    </div>
  );
};
export default ReviewLearner;
