import { Dropdown, Space } from 'antd';
import {
  Charts,
  Complex1,
  Complex23,
  Complex4,
  Complex6,
  ImageSlideItem,
  SimpleSlide,
  SimpleSlide3,
} from 'components/learner/components/slideContainer/collection';
import PopupViewHyper from 'components/learner/components/slideContainer/popupSlide/PopupViewHyper';
import { HyperNoteBody } from 'components/learner/type';
import { ComplexType, ImageSlide, SimpleType, StatisticType } from 'constants/index';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from 'store/store';

import { Lecture, SlideItem } from '@/features/courses';
import { slideInitial } from '@/features/courses/creation/constants/slide';

type SlideCollectionProps = {
  currentSlideIdx: number;
  slideItems: SlideItem[];
  selectedLecture: Lecture | null | undefined;
  setIsShowCreatNoteDialog: (value: boolean) => void;
  handleFlyToNote: (value: HyperNoteBody) => void;
  toggle: (value: boolean) => void;
  noteItems: any[];
  isShowNote: boolean;
  mode: string;
  hyperData: HyperNoteBody[];
};
const SlideCollection = (props: SlideCollectionProps) => {
  const {
    currentSlideIdx,
    slideItems,
    noteItems,
    isShowNote,
    hyperData,
    toggle,
    handleFlyToNote,
    mode,
    selectedLecture,
  } = props;

  const isInteractionWithHyper = useSelector((state: RootState) => state.learner.isInteractionWithHyper);

  const [selectedSlide, setSelectedSlide] = useState({ ...slideInitial } as SlideItem);
  const [currentHyper, setCurrentHyper] = useState<HyperNoteBody | null>(null);

  const selectedTextRef = useRef(null);

  useEffect(() => {
    const selectedSlide: SlideItem | null = slideItems[currentSlideIdx];
    if (selectedSlide) {
      setSelectedSlide(selectedSlide);
      toggle(true);
    }
  }, [currentSlideIdx, slideItems]);

  useEffect(() => {
    if (isShowNote || !isInteractionWithHyper) {
      setCurrentHyper(null);
    }
  }, [isShowNote, isInteractionWithHyper]);

  useEffect(() => {
    setCurrentHyper(null);
  }, [isInteractionWithHyper]);

  const getSlideContentByType = (selectedSlide: SlideItem) => {
    if (!selectedSlide) return null;
    const content = selectedSlide.layout_content || {};
    const { background_color, table_data } = selectedSlide;
    const { background_image_1, background_image_2, background_image_3, content_1, content_2, content_3, content_4 } =
      content?.data ?? {};

    switch (selectedSlide.layout_type_id) {
      case SimpleType.DON_GIAN_1:
        return (
          <SimpleSlide
            type={SimpleType.DON_GIAN_1}
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url || ''}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            hyperData={hyperData}
            selectedLecture={selectedLecture}
            selectedSlide={selectedSlide}
            setCurrentHyper={setCurrentHyper}
            mode={mode}
          />
        );
      case SimpleType.DON_GIAN_2:
        return (
          <SimpleSlide
            noteItems={noteItems}
            type={SimpleType.DON_GIAN_2}
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            hyperData={hyperData}
            isView={true}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            mode={mode}
          />
        );
      case SimpleType.DON_GIAN_3:
        return (
          <SimpleSlide3
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case ImageSlide.HINH_ANH_1:
        return (
          <ImageSlideItem
            type={ImageSlide.HINH_ANH_1}
            backgroundColor={background_color}
            backgroundImage1={background_image_1!}
            backgroundImage2={background_image_2!}
            backgroundImage3={background_image_3}
          />
        );
      case ImageSlide.HINH_ANH_2:
        return (
          <ImageSlideItem
            type={ImageSlide.HINH_ANH_2}
            backgroundColor={background_color}
            backgroundImage1={background_image_1}
            backgroundImage2={background_image_2}
            backgroundImage3={background_image_3}
          />
        );
      case ImageSlide.HINH_ANH_3:
        return (
          <ImageSlideItem
            type={ImageSlide.HINH_ANH_3}
            backgroundColor={background_color}
            backgroundImage1={background_image_1}
            backgroundImage2={background_image_2}
            backgroundImage3={background_image_3}
          />
        );
      case ComplexType.PHUC_TAP_1:
        return (
          <Complex1
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case ComplexType.PHUC_TAP_2:
        return (
          <Complex23
            type={ComplexType.PHUC_TAP_2}
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case ComplexType.PHUC_TAP_3:
        return (
          <Complex23
            type={ComplexType.PHUC_TAP_3}
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case ComplexType.PHUC_TAP_4:
        return (
          <Complex4
            type={ComplexType.PHUC_TAP_4}
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case ComplexType.PHUC_TAP_5:
        return (
          <Complex4
            type={ComplexType.PHUC_TAP_5}
            backgroundColor={background_color}
            fileUrl={background_image_1?.file_url!}
            title={content_1.title}
            description={content_1.description}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case ComplexType.PHUC_TAP_6:
        return (
          <Complex6
            backgroundColor={background_color}
            content1={content_1}
            content2={content_2}
            content3={content_3}
            content4={content_4}
            isView={true}
            selectedTextRef={selectedTextRef}
            noteItems={noteItems}
            selectedLecture={selectedLecture as any}
            selectedSlide={selectedSlide as any}
            setCurrentHyper={setCurrentHyper}
            hyperData={hyperData}
            mode={mode}
          />
        );
      case StatisticType.THONG_KE_1:
      case StatisticType.THONG_KE_2:
      case StatisticType.THONG_KE_3:
      case StatisticType.THONG_KE_4:
        return <Charts selectedSlide={selectedSlide as any} type={StatisticType.THONG_KE_1} isView={true} />;
      case StatisticType.THONG_KE_5:
        return (
          <div className={'h-full overflow-y-auto'}>
            <div dangerouslySetInnerHTML={{ __html: table_data?.[0]?.content || '' }} className={'table-wrapper p-6'} />
          </div>
        );
      case StatisticType.THONG_KE_6:
        return (
          <div className={'h-full overflow-y-auto'}>
            <div className={'flex h-full flex-col p-4'}>
              <Space className={'h-1/2 w-full'} direction={'vertical'}>
                <h3 className={'text-center text-[20px] font-bold text-primary'}>Bảng 1</h3>
                <div
                  dangerouslySetInnerHTML={{ __html: table_data?.[0]?.content || '' }}
                  className={'table-wrapper h-full'}
                />
              </Space>
              <Space className={'h-1/2 w-full'} direction={'vertical'}>
                <h3 className={'text-center text-[20px] font-bold text-primary'}>Bảng 2</h3>
                <div
                  dangerouslySetInnerHTML={{ __html: table_data?.[1]?.content || '' }}
                  className={'table-wrapper h-full'}
                />
              </Space>
            </div>
          </div>
        );
      default:
        return <div className={'absolute bottom-0 left-0 right-0 top-0 !bg-black'} />;
    }
  };

  return (
    <>
      <Dropdown menu={{ items: noteItems }} trigger={['contextMenu']} overlayClassName={'context-menu-learner'}>
        <div className={'slide-collection relative h-full overflow-y-hidden text-white'}>
          {getSlideContentByType(selectedSlide)}
        </div>
      </Dropdown>
      <PopupViewHyper hyper={currentHyper} setCurrentHyper={setCurrentHyper} handleFlyToNote={handleFlyToNote} />
    </>
  );
};

export default SlideCollection;
