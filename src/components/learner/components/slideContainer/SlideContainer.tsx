'use client';

import { Lecture, SlideItem } from '@/features/courses/types';
import { Audio } from 'components/learner/components/controlSlide/Audio';
import { Chapter } from 'components/learner/components/controlSlide/Chapter';
import ControlSlide from 'components/learner/components/controlSlide/ControlSlide';
import { Volume } from 'components/learner/components/controlSlide/Volume';
import PreviewSlide from 'components/learner/components/previewSlide/PreviewSlide';
import SlideContent from 'components/learner/components/slideContainer/SlideContent';
import { HyperNoteBody } from 'components/learner/type';
import { InteractionType } from 'constants/index';
import { useAudio } from 'hooks/useAudio';
import { useEffect, useState } from 'react';
import './index.scss';

type SlideContainerProps = {
  selectedLecture: Lecture | undefined | null | any;
  handleShowNote: () => void;
  hyperData: HyperNoteBody[];
  totalTime: number;
  isFullScreen: boolean;
  isShowNote: boolean;
  isPreviewMode: boolean;
  handleEndLecture: () => void;
  setIsShowCreatNoteDialog: (value: boolean) => void;
  currentSlideIdx: number;
  setCurrentSlideIdx: (value: number) => void;
  // handleSendQuestion: (value: USerAnswerQuestionBody) => void;
  handleFlyToNote: (value: HyperNoteBody) => void;
  isPreviewLearner: boolean;
};
const SlideContainer = (props: SlideContainerProps) => {
  const [mutedAudio, setMutedAudio] = useState(false);

  const {
    isPreviewMode,
    selectedLecture,
    hyperData,
    handleShowNote,
    totalTime,
    isFullScreen,
    handleEndLecture,
    setIsShowCreatNoteDialog,
    currentSlideIdx,
    setCurrentSlideIdx,
    // handleSendQuestion,
    isShowNote,
    handleFlyToNote,
    isPreviewLearner,
  } = props;

  let slideItems = [] as SlideItem[];
  const newSlides = selectedLecture?.slide?.slideItems?.slice(0, 3) || [];
  if (isPreviewLearner) {
    slideItems = newSlides;
  } else {
    slideItems = selectedLecture?.slide?.slideItems || [];
  }

  const sortedSlides = slideItems.toSorted((a, b) => a.sort_index - b.sort_index);

  const [isShowPreviewSlide, setIsShowPreviewSlide] = useState<boolean>(false);
  const selectedSlide: SlideItem | null = sortedSlides[currentSlideIdx];
  const {
    toggle,
    playing: playingAudio,
    setPlaying: setPlayingAudio,
    volume: volumeAudio,
    setVolume: setVolumeAudio,
  } = useAudio({
    idAudio: Number(selectedSlide?.id),
    url: selectedSlide?.sound?.fileUrl!,
    isLoop: !!selectedSlide?.is_loop!,
    isMuted: mutedAudio,
  });

  const [swiperRef, setSwiperRef] = useState(null);

  useEffect(() => {
    if (slideItems.length === 0) {
      setCurrentSlideIdx(-1);
    }
  }, [slideItems]);

  return (
    <div className={'slide-content-learner relative h-[calc(100vh-72px)] w-full bg-white'}>
      <SlideContent
        currentSlideIdx={currentSlideIdx}
        slideItems={sortedSlides}
        setCurrentSlideIdx={setCurrentSlideIdx}
        isFullScreen={isFullScreen}
        handleEndLecture={handleEndLecture}
        setIsShowPreviewSlide={setIsShowPreviewSlide}
        setIsShowCreatNoteDialog={setIsShowCreatNoteDialog}
        hyperData={hyperData}
        // handleSendQuestion={handleSendQuestion}
        selectedLecture={selectedLecture}
        toggle={toggle}
        isShowNote={isShowNote}
        handleFlyToNote={handleFlyToNote}
        isPreviewMode={isPreviewMode}
      />

      <div className={'absolute bottom-0 right-0 z-[1000] h-[57px] w-full'}>
        <PreviewSlide
          slide_items={slideItems}
          setSwiperRef={setSwiperRef}
          isShow={isShowPreviewSlide}
          swiperRef={swiperRef}
          currentSlideIdx={currentSlideIdx}
          setCurrentSlideIdx={setCurrentSlideIdx}
        />
        {selectedSlide?.slide_item_type_id !== InteractionType.ShortVideo && (
          <ControlSlide
            audioComponent={<Audio setPlayingAudio={setPlayingAudio} playing={playingAudio} />}
            volumeComponent={
              <Volume
                volumeAudio={volumeAudio}
                muted={mutedAudio}
                setVolumeAudio={setVolumeAudio}
                setMuted={setMutedAudio}
              />
            }
            hyperData={hyperData}
            selectedLecture={selectedLecture}
            currentSlideIdx={currentSlideIdx}
            setCurrentSlideIdx={setCurrentSlideIdx}
            isShowPreviewSlide={isShowPreviewSlide}
            setIsShowPreviewSlide={setIsShowPreviewSlide}
            totalTime={totalTime}
            isPreviewMode={isPreviewMode}
            slideItems={slideItems}
            chapterSidebarComponent={
              <Chapter
                isPreviewMode={isPreviewMode}
                isPreviewLearner={isPreviewLearner}
                handleShowNote={handleShowNote}
              />
            }
          />
        )}
      </div>
    </div>
  );
};

export default SlideContainer;
