import { processCourse } from '@/features/courses';
import { Lecture, Question, SlideItem } from '@/features/courses/types';
import { LeftOutlined, RightOutlined, StepForwardOutlined } from '@ant-design/icons';
import { But<PERSON>, Space } from 'antd';
import { MenuProps } from 'antd/es/menu/menu';
import { Video } from 'components/common/Video';
import { CaseStudyLearner, ExploreLearner } from 'components/learner/common';
import QuickQuestionLearner from 'components/learner/common/quickQuestionLearner/QuickQuestionLearner';
import SlideCollection from 'components/learner/components/slideContainer/SlideCollection';
import { HyperNoteBody } from 'components/learner/type';
import { InteractionType } from 'constants/index';
import { useQuerySearch } from 'hooks';
import { useVideoContext } from 'hooks/useVideoContext';
import AddNoteIcon from 'icons/AddNote';
import { useParams } from 'next/navigation';
import { useMutation } from 'react-query';
import { downloadFile } from 'utils';

type SlideContentProps = {
  currentSlideIdx: number;
  slideItems: SlideItem[];
  setCurrentSlideIdx: (value: number) => void;
  handleFlyToNote: (value: HyperNoteBody) => void;
  isFullScreen: boolean;
  isShowNote: boolean;
  isPreviewMode: boolean;
  handleEndLecture: () => void;
  toggle: (value: boolean) => void;
  setIsShowPreviewSlide: (value: boolean) => void;
  setIsShowCreatNoteDialog: (value: boolean) => void;
  hyperData: HyperNoteBody[];
  // handleSendQuestion: (value: USerAnswerQuestionBody) => void;
  selectedLecture: Lecture | null | undefined;
};
const SlideContent = (props: SlideContentProps) => {
  const {
    currentSlideIdx,
    handleFlyToNote,
    isShowNote,
    slideItems,
    isPreviewMode,
    setCurrentSlideIdx,
    handleEndLecture,
    setIsShowCreatNoteDialog,
    hyperData,
    selectedLecture,
    toggle,
  } = props;

  const videoRef = useVideoContext();

  const params: { courseId: string } = useParams();

  const { mutate: processCourseMutation } = useMutation({
    mutationFn: (body: { courseId: string; sectionId: string; lectureId: string; isCompleted: boolean }) =>
      processCourse(body),
  });

  const { onHoverVideo, onFocusOutVideo } = videoRef;

  const searchQuery = useQuerySearch();
  const { lectureId, mode } = searchQuery;
  const selectedSlide: SlideItem | null = slideItems[currentSlideIdx];

  const onNextQuestion = () => {
    if (slideItems.length - 1 === currentSlideIdx) {
      setCurrentSlideIdx(0);
      handleEndLecture();
    } else setCurrentSlideIdx(currentSlideIdx + 1);
  };

  const noteItems: MenuProps['items'] = isPreviewMode
    ? []
    : [
        {
          label: 'Tạo ghi chú nhanh',
          key: '1',
          icon: <AddNoteIcon />,
          onClick: () => {
            // TODO tạm thời chưa tạo đc hyper bôi đen do solution đang gặp lỗi
            // return;
            setIsShowCreatNoteDialog(true);
          },
        },
      ];

  const handleDownloadFile = (file: { file_url: string; file_name: string }) => {
    downloadFile(file.file_url, file.file_name)
      .then(() => {})
      .catch(() => {
        throw new Error('Download file failed');
      });
  };

  const getSlideContentByType = () => {
    if (!selectedSlide) return <></>;
    switch (selectedSlide.slide_item_type_id) {
      case InteractionType.Default:
        return (
          <SlideCollection
            currentSlideIdx={currentSlideIdx}
            selectedLecture={selectedLecture}
            slideItems={slideItems}
            noteItems={noteItems}
            setIsShowCreatNoteDialog={setIsShowCreatNoteDialog}
            hyperData={hyperData}
            toggle={toggle}
            isShowNote={isShowNote}
            handleFlyToNote={handleFlyToNote}
            mode={mode || ''}
          />
        );
      case InteractionType.Question:
        return (
          <QuickQuestionLearner
            question={selectedSlide?.question || ({} as Question)}
            onNextQuestion={(interaction, answers) => {
              // const body: USerAnswerQuestionBody = {
              //   question_id: selectedSlide.question_id!,
              //   lecture_id: +lectureId!,
              //   answer: answers,
              //   lecture_interact_id: selectedSlide.slide_item_type_id,
              // };
              // handleSendQuestion(body);

              onNextQuestion();
            }}
            userAnswers={selectedLecture?.user_answers}
            noteItems={noteItems}
            isLearner={true}
            bgColor={selectedSlide.background_color || '#ffffff'}
          />
        );
      case InteractionType.Explore:
        return (
          <ExploreLearner
            explore={selectedSlide.explore!}
            onNextQuestion={onNextQuestion}
            handleDownloadFile={handleDownloadFile}
            noteItems={noteItems}
          />
        );
      case InteractionType.Situation:
        return (
          <CaseStudyLearner
            caseStudy={selectedSlide.case_study!}
            onNextQuestion={onNextQuestion}
            noteItems={noteItems}
            isLearner={true}
          />
        );
      case InteractionType.ShortVideo:
        // setIsShowPreviewSlide(false);
        return (
          <div className={'relative h-full w-full'}>
            <>
              <Button
                type={'primary'}
                className={
                  'btn-disable-learner absolute right-[30px] top-[30px] z-[1000] flex items-center bg-[#434b61cc] text-white'
                }
                onClick={() => onNextQuestion()}
              >
                <span>Bỏ qua</span>
                <StepForwardOutlined className={'text-white'} />
              </Button>
              <Video urlFile={selectedSlide.short_clip?.file_url} />
              <div className={'absolute bottom-[15px] left-[50%] z-[9999] translate-x-[-50%]'}>
                <Space size={32} className={'flex w-full justify-center'}>
                  {currentSlideIdx > 0 ? (
                    <div
                      className={'flex cursor-pointer gap-3 text-white'}
                      onClick={() => {
                        setCurrentSlideIdx(currentSlideIdx - 1);
                      }}
                    >
                      <LeftOutlined />
                      <p>Slide trước</p>
                    </div>
                  ) : (
                    <div className={'w-[80px]'}></div>
                  )}
                  {currentSlideIdx < slideItems.length - 1 ? (
                    <div className={'pointer flex gap-3 text-white'} onClick={onNextQuestion}>
                      <p>Slide sau</p>
                      <RightOutlined />
                    </div>
                  ) : (
                    <div className={'w-[80px]'}></div>
                  )}
                </Space>
              </div>
            </>
          </div>
        );
    }
  };

  return (
    <div className={'slide-collection h-full w-full'}>
      {selectedSlide?.slide_item_type_id !== InteractionType.ShortVideo && (
        <div className={'top-space h-[70px] bg-black'} />
      )}
      <div
        className={`h-[calc(100vh-199px)] ${
          selectedSlide?.slide_item_type_id === InteractionType.ShortVideo ? 'h-full' : ''
        } `}
        style={{ overflowY: selectedSlide?.slide_item_type_id === InteractionType.ShortVideo ? 'hidden' : 'auto' }}
      >
        {getSlideContentByType()}
      </div>
      <div className={'bottom-space h-[65px] bg-black'} />
    </div>
  );
};

export default SlideContent;
