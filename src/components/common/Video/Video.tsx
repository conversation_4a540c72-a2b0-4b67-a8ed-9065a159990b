'use client';

import { LectureDetail } from '@/features/courses/types/lecture.type';
import Tooltip from 'antd/es/tooltip';
import { BufferingSpin } from 'components/buffering-spin';
import PauseModal from 'components/learner/components/pauseModal/PauseModal';
import { useClient } from 'hooks/useClient';
import { useControlVideo } from 'hooks/useControlVideo';
import { useVideoContext } from 'hooks/useVideoContext';
import CloseIcon from 'icons/CloseIcon';
import dynamic from 'next/dynamic';
import ReactPlayer from 'react-player/lazy';
import { useSelector } from 'react-redux';
import { RootState } from 'store/store';
import './index.scss';

const ControlVideo = dynamic(() => import('components/control-video'), {
  ssr: false,
});

const TextEditor = dynamic(
  () => import('components/courses/CoureContent/SectionEditLayout/Aside/textEditor/TextEditor'),
  {
    ssr: false,
  },
);

type VideoPlayingProps = {
  selectedLecture?: LectureDetail;
  urlFile?: string;
  showPauseVideo?: boolean;
  isShow?: boolean;
  showDelete?: boolean;
  className?: string;
  onDelete?: () => void;
  scaleIcon?: number;
};

export const Video = (props: VideoPlayingProps) => {
  const {
    selectedLecture,
    urlFile,
    isShow = true,
    onDelete,
    className,
    showDelete = false,
    scaleIcon = 1,
    showPauseVideo = true,
  } = props;
  const {
    playerProgress,
    handleChangeProgress,
    setPlayerProgress,
    onBuffer,
    handleVideoReady,
    onBufferEnd,
    videoRef: playingRef,
    onHoverVideo,
    onFocusOutVideo,
  } = useVideoContext();
  const { fullScreenRef, isFullScreen } = useControlVideo();

  const { isClient } = useClient();

  const selectedIndex = useSelector((state: RootState) => state.textEditor.selectedIndex);
  const segmentModal = useSelector((state: RootState) => state.course.segmentModal);
  const isShowControlVideo = !segmentModal.isOpen;

  const CloseIconVideo = () =>
    showDelete && !isFullScreen ? (
      <Tooltip title={'Xóa video'}>
        <span className={'absolute right-3 top-3 z-[100] cursor-pointer'} onClick={onDelete}>
          <CloseIcon />
        </span>
      </Tooltip>
    ) : null;

  return isShow ? (
    <div
      onMouseLeave={onFocusOutVideo}
      onMouseEnter={onHoverVideo}
      className={`wrap-playing video-common relative h-full cursor-pointer rounded-xl ${className}`}
      ref={fullScreenRef}
    >
      <CloseIconVideo />
      {(selectedLecture?.video?.fileUrl || urlFile) && (!playerProgress.ready || playerProgress.buffering) && (
        <BufferingSpin />
      )}
      {isClient && (
        <div
          className={'h-full rounded-xl'}
          onClick={() =>
            setPlayerProgress({
              ...playerProgress,
              playing: !playerProgress.playing,
            })
          }
        >
          <ReactPlayer
            ref={playingRef}
            url={selectedLecture?.video?.fileUrl ?? (urlFile as string)}
            playing={playerProgress.playing}
            muted={playerProgress.muted}
            className="react-player"
            width="100%"
            height="100%"
            onReady={handleVideoReady}
            volume={playerProgress.volume / 100}
            onBuffer={onBuffer}
            onBufferEnd={onBufferEnd}
            onProgress={handleChangeProgress}
          />
        </div>
      )}
      {selectedIndex > -1 && <TextEditor isShow />}
      {playerProgress.ready && showPauseVideo && (
        <PauseModal
          setPlaying={(newPlaying) => {
            setPlayerProgress({
              ...playerProgress,
              playing: newPlaying,
            });
          }}
          isShow={!playerProgress.playing}
          scaleIcon={scaleIcon}
        />
      )}
      {isShowControlVideo && (
        <ControlVideo
          isPreviewMode
          hyperData={[]}
          setSelectedInteractionVideo={() => {}}
          selectedLecture={undefined}
          handleShowNote={() => {}}
          isFullScreen={isFullScreen}
          currentSegmentName={''}
          fullScreenRef={fullScreenRef}
        />
      )}
    </div>
  ) : (
    <></>
  );
};

export default Video;
