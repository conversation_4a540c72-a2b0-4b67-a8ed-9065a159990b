'use client';

import { UserCourse } from '@/features/courses';
import { ArrowRightOutlined } from '@ant-design/icons';
import { Progress, Space } from 'antd';
import { routePaths } from 'config';
import { useRouter } from 'next-nprogress-bar';
import Image from 'next/image';
import querystring from 'query-string';
import { useTransition } from 'react';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { ImageCourseCarousel, notifyWarning } from 'utils';
import { StarBorderOutlined } from '../../icons';
import { Button } from '../../lib';
import { getSectionName } from '../learner/convert';

import 'swiper/css';
import 'swiper/css/navigation';
import { MINUTE } from '../../constants/time';

export function ProfileCarousel({
  userCourses,
  buttonTitle,
  isRetake = false,
}: Readonly<{
  userCourses: UserCourse[];
  buttonTitle: string;
  isRetake?: boolean;
}>) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const onGotoLastLecture = (userCourse: UserCourse) => {
    startTransition(() => {
      const { lastViewLecture, courseId } = userCourse;

      if (!lastViewLecture.id) {
        notifyWarning('Không tìm thấy bài học gần nhất!');
        return;
      }

      const path = querystring.stringifyUrl({
        url: routePaths.learner.path.replace(':courseId', courseId),
        query: { sectionId: lastViewLecture.sectionTypeId, lectureId: lastViewLecture.id },
      });

      router.push(path);
    });
  };

  return (
    <Swiper
      modules={[Navigation]}
      navigation
      key={'slider'}
      slidesPerView={'auto'}
      className={'swiper-custom-navigation'}
      // onInit={() => setInit(true)}
    >
      {userCourses.map((userCourse, index) => {
        const { lastViewLecture, countCompletedLectures, courses: course } = userCourse;
        const { courseThumbnailImage, courseName } = userCourse.courses ?? {};
        const totalLectures = Number(course.totalSections);
        const learningPercent = countCompletedLectures / totalLectures;

        return (
          <SwiperSlide className={'mr-6 lg:!w-[630px]'} key={userCourse.id}>
            <div className={`h-full rounded-xl p-6 ${index % 2 === 0 ? 'bg-primary-100' : 'bg-secondary-100'}`}>
              <div className="grid grid-cols-1 gap-5 lg:grid-cols-3">
                {courseThumbnailImage ? (
                  <div
                    className="m-1 rounded-md bg-center md:h-[250px] md:w-[180px]"
                    style={{ backgroundImage: `url(${courseThumbnailImage})` }}
                  ></div>
                ) : (
                  <Image
                    alt={'ImageCourseCarousel'}
                    width={180}
                    height={250}
                    className="mt-2 rounded-[10px] object-cover"
                    src={courseThumbnailImage ?? ImageCourseCarousel}
                  />
                )}

                <div className="lg:col-span-2">
                  <Space direction={'vertical'} className="w-full" size={24}>
                    <div>
                      <Progress showInfo={false} percent={learningPercent * 100} />
                    </div>
                    <Space className="w-full" direction={'vertical'} size={8}>
                      <div className="line-clamp-1 text-sm font-medium uppercase text-ink-700">{courseName}</div>
                      <div className="line-clamp-2 text-xl font-semibold text-ink-black md:text-2xl">
                        {lastViewLecture?.section?.sectionName}
                      </div>
                      <div className="text-sm text-ink-800">
                        <Space>
                          <div className={'line-clamp-2'}>
                            {getSectionName({ sectionTypeId: lastViewLecture.sectionTypeId } as any)}
                          </div>
                          <StarBorderOutlined />
                          <div>
                            {(course.courseDuration ? Number(course.courseDuration) / MINUTE : 1).toFixed()} phút
                          </div>
                        </Space>
                      </div>
                    </Space>
                    <Button type={'primary'} onClick={() => onGotoLastLecture(userCourse)} loading={isPending}>
                      <Space>
                        {buttonTitle} <ArrowRightOutlined />
                      </Space>
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
}
