const BASE_API_URL_V1 = process.env.NEXT_PUBLIC_BASE_API_URL_V1;

export const getApiUrlV1 = (endpoint: string): string => `${BASE_API_URL_V1}${endpoint}`;

const API_ROOT_ENDPOINTS = {
  USERS: getApiUrlV1('/users'),
  COURSES: getApiUrlV1('/courses'),
};

const USER_FILE = `${API_ROOT_ENDPOINTS.USERS}/files`;

export const API_ENDPOINTS = {
  USERS: {
    GET: {
      GOOGLE_LOGIN: `${API_ROOT_ENDPOINTS.USERS}/google/login`,
      OUTSTANDING_CREATORS: `${API_ROOT_ENDPOINTS.USERS}/outstanding-authors`,
      FILES: USER_FILE,
    },
    POST: {
      LOGIN: `${API_ROOT_ENDPOINTS.USERS}/login-by-email`,
      LOGOUT: `${API_ROOT_ENDPOINTS.USERS}/logout`,
      REGISTER: `${API_ROOT_ENDPOINTS.USERS}`,
      RESET_PASSWORD: `${API_ROOT_ENDPOINTS.USERS}/reset-password`,
      CONFIRM_EMAIL: `${API_ROOT_ENDPOINTS.USERS}/confirm`,
      RESEND_EMAIL: `${getApiUrlV1('/resend-email')}`,
      UPDATE_PROFILE: `${API_ROOT_ENDPOINTS.USERS}/profile`,
      UPLOAD_FILE: USER_FILE,
      UPLOAD_FILE_IN_CHUNK: `${USER_FILE}/chunk`,
    },
    PUT: {
      CONFIRM_PASSWORD: `${API_ROOT_ENDPOINTS.USERS}/reset-password`,
      UPDATE_FILE: USER_FILE,
    },
    DELETE: {
      DELETE_FILE: `${USER_FILE}/:id`,
    },
    PATCH: {
      RENAME_FILE: `${USER_FILE}/:id`,
    },
  },

  COURSES: {
    GET: {
      COURSES: `${API_ROOT_ENDPOINTS.COURSES}`,
      USER_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/user-course`,
      NEW_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/new-courses`,
      OUTSTANDING_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/outstanding-courses`,
      SEARCH_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/search-courses`,
      TOPICS: `${API_ROOT_ENDPOINTS.COURSES}/topics`,
      COURSES_BY_CREATOR: `${API_ROOT_ENDPOINTS.COURSES}/creator/:userId`,
      COURSE_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId`,
      COURSE_DETAIL_BY_AUTH: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/auth`,
      TAGS: getApiUrlV1('/tags'),
      ALL_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/auth`,
      LECTURE_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      FAVORITE_COURSES: `${API_ROOT_ENDPOINTS.COURSES}/favorites`,
      FAVORITE_CREATORS: `${API_ROOT_ENDPOINTS.COURSES}/authors/favorites`,
      SECTION_TEST_DETAIL: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId`,
      LEANER_TEST_ANSWER: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId/answers`,
    },
    POST: {
      CREATE_COURSE: `${API_ROOT_ENDPOINTS.COURSES}`,
      CREATE_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/sections`,
      DELETE_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId`,
      CREATE_TAG: getApiUrlV1('/tags'),
      UPLOAD: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/upload`,
      CREATE_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures`,
      CREATE_SLIDE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/slide`,
      CREATE_SLIDE_ITEM: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/slide/:slideId`,
      FAVORITE_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/favorite`,
      FAVORITE_CREATOR: `${API_ROOT_ENDPOINTS.COURSES}/authors/:authorId/favorite`,
      CREATE_QUESTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/questions`,
      CREATE_QUESTION_TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests`,
      PROCESS_LEARNER_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      LEANER_TEST_ANSWER: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId/answers`,
      COURSE_REVIEW: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/reviews`,
    },
    PUT: {
      EDIT_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId`,
      EDIT_SECTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId`,
      EDIT_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
    },
    PATCH: {
      PUBLISH_COURSE: `${API_ROOT_ENDPOINTS.COURSES}/publish/:courseId`,
      UPDATE_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
    },
    DELETE: {
      DELETE_LECTURE: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId`,
      DELETE_SLIDE: `${API_ROOT_ENDPOINTS.COURSES}/lectures/:lectureId/slide/:slideId/slide-items/:slideItemId`,
      DELETE_VIDEO_INTERACTION: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/lectures/:lectureId/interactions/:interactId`,
      DELETE_QUESTION_TEST: `${API_ROOT_ENDPOINTS.COURSES}/:courseId/sections/:sectionId/tests/:testId/questions/:questionId`,
    },
  },
};

export enum HTTP_STATUS_CODE {
  SUCCESS = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500,
}
