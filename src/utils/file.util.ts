export async function transformImageUrlToFile(url: string) {
  const fileExtension = url.split('.').pop()?.toLowerCase();
  const mimeType = fileExtension === 'jpg' ? 'image/jpeg' : `image/${fileExtension}`;
  const fileName = url.split('/').pop()?.split('.')[0] || '';

  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error('Failed to fetch image');

    const blob = await response.blob();

    const file = new File([blob], `${fileName}.${fileExtension}`, {
      type: blob.type || mimeType,
    });

    return file;
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}

export const downloadFile = async (url: string, name: string) => {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/octet-stream',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download file');
    }

    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = name;
    link.click();

    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Download error:', error);
    throw error;
  }
};

export const convertImageFileToBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};
