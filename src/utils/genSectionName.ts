import { LectureInteract, Section } from '@/features/courses/types';
import { LectureType, SectionType } from 'config';

const lectureNames = [
  {
    key: LectureType.Target,
    label: '<PERSON><PERSON><PERSON> tiêu học tập',
  },
  {
    key: LectureType.Result,
    label: '<PERSON><PERSON><PERSON> quả học tập',
  },
  {
    key: LectureType.Test,
    label: '<PERSON>ài kiểm tra trong chương',
  },
  {},
];

const sectionNameObject = {
  [SectionType.Target]: '<PERSON>ụ<PERSON> tiêu học tập',
  [SectionType.Test]: '<PERSON>à<PERSON> kiểm tra cuối khóa học',
  [SectionType.Result]: '<PERSON>ết quả học tập',
  [SectionType.Default]: '',
};
export function genSectionName(
  props: Readonly<{
    section?: Section;
    lectureType?: LectureType;
    selectedInteractionVideo?: LectureInteract;
  }>,
) {
  const { section: chapter, lectureType, selectedInteractionVideo } = props;
  if (!chapter || !lectureType) return '';
  const sectionName = sectionNameObject[chapter.sectionTypeId];
  if (sectionName !== '') {
    return sectionName;
  }

  const lectureName = lectureNames.find((lectureName) => lectureName.key === lectureType)?.label;

  if (lectureName) {
    return lectureName;
  }

  return !selectedInteractionVideo ? chapter.sectionName : selectedInteractionVideo.interact_name;
}
