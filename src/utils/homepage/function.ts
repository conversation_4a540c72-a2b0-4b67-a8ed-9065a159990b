import { CourseInfo, LectureDetail } from '@/features/courses';
import { message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { flatten } from 'lodash-es';
import { Discussion } from 'type/homepage';
import { maxFileSize } from 'utils/validate';

const getFavouriteStatus = (id: number, ids: number[]) => ids.includes(id);

const getTotalLectureInCourse = (courseInfo: CourseInfo) => {
  return (
    Array.isArray(courseInfo.sections) &&
    courseInfo.sections.reduce((total, section) => {
      return total + section.lectures.length;
    }, 0)
  );
};

const getPreviousLecture = (courseInfo: CourseInfo, currentLecture: LectureDetail) => {
  const lectures = { ...courseInfo }.sections?.map((section, sectionIdx) => {
    return section.lectures.map((lecture, lectureIdx) => ({
      ...lecture,
      idx: `${sectionIdx.toString() + lectureIdx.toString()}`,
    }));
  });
  const currentIdx = flatten(lectures).findIndex((lecture) => lecture.id === currentLecture.id);
  return flatten(lectures)[currentIdx - 1];
};

const formatDate = (date: string) => {
  if (!date) return '';
  return dayjs(date).format('DD/MM/YYYY, HH:MM');
};

const countDiscussion = (discussion: Discussion[]) => {
  const discussions = discussion ?? [];
  const totalQuestion = discussions.length;
  const totalComment = discussions.reduce((total: number, item: Discussion) => (total += item.children.length), 0);
  return {
    totalQuestion,
    totalComment,
  };
};

function formatDateOfBirth(dateString: string | null) {
  // Tạo một đối tượng Date từ chuỗi ngày
  if (!dateString) {
    return null;
  }
  const dateObject = new Date(dateString);

  // Lấy ngày, tháng và năm từ đối tượng Date
  const day = dateObject.getDate();
  const month = dateObject.getMonth() + 1; // Tháng bắt đầu từ 0, cần cộng thêm 1
  const year = dateObject.getFullYear();

  // Đảm bảo ngày và tháng có dạng hai chữ số
  const formattedDay = day < 10 ? '0' + day : day;
  const formattedMonth = month < 10 ? '0' + month : month;

  // Trả về chuỗi ngày mới có định dạng MM-DD-YYYY
  return `${formattedMonth}-${formattedDay}-${year}`;
}

const validateBirthDay = (value: Dayjs) => {
  const selectedDate = dayjs(value);
  const currentDate = dayjs();
  const diffDate = selectedDate.diff(currentDate, 'days');
  if (diffDate > 0) {
    return Promise.reject('Ngày sinh không được lớn hơn ngày hiện tại');
  }
  return Promise.resolve();
};

const validateMaxSize = (file: File, maxSize: number) => {
  const messageMaxSize = maxFileSize(file.size, 'byte', maxSize);
  if (messageMaxSize) {
    message.warning(messageMaxSize);
    return false;
  }
  return true;
};

function byteToGB(byte: number) {
  return Math.round(byte / 1073741824).toFixed(0);
}

function GBToByte(GB: number) {
  return Math.round(GB * 1073741824).toFixed(2);
}

function joinString(arr: string[]) {
  if (arr.length === 0) {
    return '';
  } else if (arr.length === 1) {
    return arr[0];
  } else {
    return arr.slice(0, -1).join(', ') + ' và ' + arr[arr.length - 1];
  }
}

export {
  GBToByte,
  byteToGB,
  countDiscussion,
  formatDate,
  formatDateOfBirth,
  getFavouriteStatus,
  getPreviousLecture,
  getTotalLectureInCourse,
  joinString,
  validateBirthDay,
  validateMaxSize,
};
