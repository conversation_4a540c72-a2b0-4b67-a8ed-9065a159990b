export function secondsToTimestamp(seconds: number) {
  if (!seconds || seconds <= 0) return 0;
  const millisecond = seconds * 1000;
  const currentTimestamp = Date.now();
  return Math.floor(currentTimestamp + millisecond);
}

export function timestampToSeconds(timestamp: number) {
  if (!timestamp || timestamp <= 0) return 0;
  const currentTimestamp = Date.now();
  const millisecond = timestamp - currentTimestamp;
  return Math.floor(millisecond / 1000);
}
