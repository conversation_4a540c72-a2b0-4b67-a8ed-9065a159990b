import { LectureType, SectionType } from 'config';
import { CopyrightEnum, CourseType, PackageId } from 'constants/enum';
import { Target } from 'type';
import { CreateTagRes } from 'type/api';
import { UserInfo } from 'type/common';
import { CaseStudyItem, Explore, Question } from 'type/course/interaction';
import { LectureInteract } from 'type/course/lectureInteraction';
import { LectureDocument, LectureSegment } from './lectureSegment';
import { SlideItem } from './slide';
import { Test } from './test';

export interface MasterDataCourse {
  packages: Packages[];
  authors: UserInfo[];
  tags: Tags[];
  course_levels: CourseLevel[];
  topic: Topic[];
  organizations: Organization[];
  certificates: Certificate[];
  feelings: Feeling[];
}

export interface Feeling {
  code: string;
  id: number;
  name: string;
}

export interface Certificate {
  id: number;
  certificate_name: string;
  certificate_description: string;
  created_by: null | string;
  updated_by: null | string;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: number;
  organization_name: string;
  organization_description: string;
  created_by: null | string;
  updated_by: null | string;
  created_at: string;
  updated_at: string;
}

export interface Packages {
  id: number;
  package_name: string;
  package_description: string;
  created_at: null | number | string;
  updated_at: null | number | string;
  is_vip: 0 | 1;
}

export interface Topic {
  id: number;
  topic_name: string;
}

export interface Tags {
  id: number;
  tag_name: string;
}

export interface CourseLevel {
  id: number;
  course_level_name: string;
}

export interface CourseRelated extends FullCourseInfo {
  id: number;
  package_id: PackageId;
  course_type: number | string | null;
  course_name: string;
  course_description: string;
  course_thumbnail_image: string;
  duration: number;
  copyright: null;
  topic_id: number;
  course_level_id: number;
  certificate_id: null;
  organization_id: null;
  sort_index: null;
  publish: number;
  created_by: number;
  updated_by: null;
  created_at: number | string;
  updated_at: number | string;
}

export interface Lecture {
  id: number;
  section_id: number;
  lecture_thumbnail_image: string | null;
  lecture_name: string;
  sort_index: number;
  created_by: number;
  updated_by: null;
  created_at: number;
  updated_at: number;
  lecture_type_id: LectureType;
  video_id: null | number;
  video: null | CourseFile;
  lecture_segments: LectureSegment[];
  lecture_documents: LectureDocument[];
  slide_id?: number;
  slide: {
    id: number;
    slide_items: SlideItem[];
  } | null;
  test_id?: number;
  test: Test | null;
  lecture_interacts: LectureInteract[];
  publish?: number;
  user_answers?: UserAnswer[];
}

export interface UserAnswer {
  id: number | string;
  lecture_id: number;
  section_id: number;
  user_id: number;
  test_id: null | number;
  question_id: number;
  case_study_id: null | number;
  lecture_interact_id: null | number;
  times: number;
  answer: string;
  point: number;
}

export enum SectionPublish {
  UnPublish,
  Publish,
}

export interface Section {
  section_thumbnail_image: string;
  id: number;
  course_id: number;
  publish: SectionPublish;
  section_type_id: SectionType;
  section_name: string;
  sort_index: number;
  created_by: number;
  updated_by: null;
  created_at: number;
  updated_at: number;
  lectures: Lecture[];
  slide: {
    id?: number;
    slide_items: SlideItem[];
  } | null;
  test_id?: number;
  test: Test | null;
  learning_goal: Target;
  section_duration: number;
}

export interface FAQ {
  id: number;
  course_id: number;
  question: string;
  answer: string;
  created_by: number;
  updated_by: number | null;
  created_at: string;
  updated_at: string | null;
}

export enum CourseActive {
  InActive,
  Active,
}

export type CourseInfo = {
  id?: number;
  total_lectures?: number;
  slug?: string;
  package_id: PackageId;
  course_type: null | string | number;
  course_name: string;
  course_description: string;
  course_thumbnail_image: null | string;
  current_user_registered: CurrentUserRegisterProps | null;
  duration: number;
  current_user_favorite?: number;
  active: CourseActive;
  copyright: null | CopyrightEnum;
  organization_name: null | string;
  organization_description: null | string;
  topic_id: number;
  course_level_id: number;
  course_type_id: CourseType;
  sort_index: null;
  publish: number;
  created_by: null | string | number;
  updated_by: null | string | number;
  created_at: string | number;
  updated_at: number | string;
  certificate_id: string | null;
  organization_id: string | null;
  authors: UserInfo[];
  user?: UserInfo;
  course_faq: FAQ[];
  course_related: CourseRelated[];
  tags?: Tags[];
  is_sequential?: number;
  course_duration?: number;
  total_learner?: number | null;
  total_rating_1?: number | null;
  total_rating_2?: number | null;
  total_rating_3?: number | null;
  total_rating_4?: number | null;
  total_rating_5?: number | null;
  total_sections: number;
  avg_rating?: number | null;
  total_rating?: number | null;
  current_user_rated_count?: number;
  current_user_favourite_count?: number;
  current_user_rated?: UserInfo | null;
  topic?: Topic;
};

export interface FullCourseInfo extends CourseInfo {
  sections: Section[];
  count_hyper_notes?: number | null;
  count_feeling_1: number;
  count_feeling_2: number;
  count_feeling_3: number;
  count_feeling_4: number;
}

export interface FullCourseDiscussion extends FullCourseInfo {
  id?: number;
  course_id?: number;
  count_questions: number;
  count_answers: number;
}

export interface PublishCourse extends CourseInfo {
  sections: Section[];
  topic: Topic;
  tag: CreateTagRes[];
}

export interface LastCourseView {
  course: FullCourseInfo;
  section: Section;
  lecture: Lecture;
}

export interface ContentSidebar {
  key?: number;
  header: string;
  children?: { chapter: string }[] | null;
}

export type UpdateLectureType = {
  chapter?: string;
  lecture?: string;
  lecture_thumbnail_image?: File | undefined | null;
  file_id?: number | null | undefined;
};

export type CourseFile = {
  file_duration: string | number;
  file_name: string;
  file_size: string | number;
  file_type_id: number;
  file_url: string;
  id: number;
};

export type Index = {
  id: number;
  sort_index: number;
};

export type Swap = Index[];

export type SwapChapter = {
  data_update: Swap;
};

export type SwapLecture = {
  data_update: Swap;
};

export type VideoInteraction = Question | Explore | CaseStudyItem | null;

export enum IsCompletedCourse {
  NotCompleted,
  Completed,
}

export type CurrentUserRegisterProps = {
  id: number;
  user_id: number;
  course_id: number;
  is_completed: IsCompletedCourse;
  last_view_lecture_id: 12;
  created_at: '2024-06-25T21:00:41.000000Z';
  updated_at: '2024-06-25T21:00:41.000000Z';
};

// export type UserCourse = {
//   count: number;
//   limit: number;
//   page: number;
//   data: Array<{
//     id: string;
//     user: {
//       id: string;
//       name: string;
//       email: string;
//     };
//     courseId: string;
//     isCompleted: number;
//     lastViewLecture: {
//       id: string;
//       lectureName: string;
//       lectureThumbnailImage: string;
//       sectionTypeId: number;
//     };
//     totalLectures: number;
//     countCompletedLectures: number;
//     createdAt: string;
//     updatedAt: string;
//     courses: {
//       id: string;
//       courseName: string;
//       courseDescription: string;
//       courseThumbnailImage: string;
//       courseLevelId: string;
//       courseDuration: string;
//       totalSections: string;
//       totalLearner: string;
//       topic: {
//         id: string;
//         topicName: string;
//         showOnBoard: number;
//       };
//     };
//   }>;
// };
