{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  },

  "typescript.preferences.includePackageJsonAutoImports": "on",

  "tailwindCSS.includeLanguages": {
    "plaintext": "html",
    "javascript": "javascript",
    "javascriptreact": "javascript",
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },

  "tailwindCSS.classFunctions": ["clsx", "cn"],
  "tailwindCSS.suggestions": true,

  "todo-tree.general.tags": [
    "BUG",
    "FIX",
    "TODO",
    "TEST",
    "MOCK",
    "REFACTOR",
    "IMPROVE",
    "DEPRECATED",
    "FUTURE",
    "EXAMPLE",
    "WORKAROUND"
  ],

  "todo-tree.highlights.customHighlight": {
    // Icon from: https://primer.style/design/foundations/icons
    "TODO": {
      "icon": "check-circle",
      "iconColour": "#007460",
      "background": "#007460",
      "foreground": "#FFF"
    },
    "BUG": {
      "icon": "bug",
      "iconColour": "#B72136",
      "background": "#B72136",
      "foreground": "#FFF"
    },
    "FIX": {
      "icon": "flame",
      "iconColour": "#E25822",
      "background": "#E25822",
      "foreground": "#FFF"
    },
    "TEST": {
      "icon": "beaker",
      "iconColour": "#B78103",
      "background": "#B78103",
      "foreground": "#FFF"
    },
    "MOCK": {
      "icon": "pencil",
      "iconColour": "#B78103",
      "background": "#B78103",
      "foreground": "#FFF"
    },
    "REFACTOR": {
      "icon": "tools",
      "iconColour": "#0FBDCC",
      "background": "#0FBDCC",
      "foreground": "#011C37"
    },
    "IMPROVE": {
      "icon": "rocket",
      "iconColour": "#DF2A98",
      "background": "#DF2A98",
      "foreground": "#FFF"
    },
    "FUTURE": {
      "icon": "light-bulb",
      "iconColour": "#02a4af",
      "background": "#02a4af",
      "foreground": "#FFF"
    },
    "DEPRECATED": {
      "icon": "stop",
      "iconColour": "#851515",
      "background": "#851515",
      "foreground": "#FFF"
    },
    "EXAMPLE": {
      "icon": "book",
      "iconColour": "#416d50",
      "background": "#416d50",
      "foreground": "#FFF"
    },
    "WORKAROUND": {
      "icon": "issue-draft",
      "iconColour": "#fa6b9a",
      "background": "#fa6b9a",
      "foreground": "#011C37"
    }
  },
  "todo-tree.filtering.excludeGlobs": ["**/node_modules/*/**"],
  "todo-tree.tree.flat": true,
  "todo-tree.general.statusBar": "tags",
  "todo-tree.tree.groupedByTag": true,
  "todo-tree.tree.buttons.scanMode": true,
  "todo-tree.tree.scanMode": "workspace",

  "cSpell.words": ["studify"]
}
